---@meta

---@class TheFrontEnd
TheFrontEnd = {}

---
---UNKNOWN
---
---author:
function TheFrontEnd:PopScreen() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:PushScreen() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetIsOfflineMode() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:ToggleImgui() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:IsDebugPanelOpen() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:CreateDebugPanel() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetActiveScreen() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:SetFadeLevel() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:Fade() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:OnSaveLoadError() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:ShowScreen() end

---
---获取画面设置
---@return GraphicsOptions # 画面设置
---@nodiscard
---author:
function TheFrontEnd:GetGraphicsOptions() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:ClearScreens() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetSound() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:StopTrackingMouse() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:OnControl() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:OnMouseMove() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:OnMouseButton() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:ShowSavingIndicator() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:HideSavingIndicator() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:SetServerPauseText() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:IsImGuiWindowFocused() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:IsControlsDisabled() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:SendScreenEvent() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:FindOpenDebugPanel() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetScreenStackSize() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetAccountManager() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetFadeLevel() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:OnRenderImGui() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:Update() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:LockFocus() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetFocusWidget() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:ShowConsoleLog() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:HideConsoleLog() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetHUDScale() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:DoFadeIn() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:FadeBack() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:SetOfflineMode() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:HideTopFade() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:InsertScreenUnderTop() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetOpenScreenOfType() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:UpdateRepeatDelays() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetProportionalHUDScale() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:FadeToScreen() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:OnFocusMove() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:SetConsoleLogPosition() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:ClearFocus() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetTwitchOptions() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:GetCraftingMenuScale() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:SetForceProcessTextInput() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:DoHoverFocusUpdate() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:StartUpdatingWidget() end

---
---UNKNOWN
---
---author:
function TheFrontEnd:StopUpdatingWidget() end

