---@meta

---@class replica_stackable
local replica_stackable = {}

---
---author: 
function replica_stackable:OnRemoveFromEntity()
end

---
---author: 
function replica_stackable:MaxSize()
end

---
---author: 
function replica_stackable:IsOverStacked()
end

---
---author: 
function replica_stackable:OriginalMaxSize()
end

---
---author: 
function replica_stackable:IsFull()
end

---
---author: 
function replica_stackable:IsStack()
end

---
---@param stacksize idk # 
---author: 
function replica_stackable:SetStackSize(stacksize)
end

---
---author: 
function replica_stackable:ClearPreviewStackSize()
end

---
---author: 
function replica_stackable:StackSize()
end

---
---@param maxsize idk # 
---author: 
function replica_stackable:SetMaxSize(maxsize)
end

---
---author: 
function replica_stackable:GetPreviewStackSize()
end

---
---@param ignoremaxsize idk # 
---author: 
function replica_stackable:SetIgnoreMaxSize(ignoremaxsize)
end

