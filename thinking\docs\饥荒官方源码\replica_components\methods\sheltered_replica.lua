---@meta

---@class replica_sheltered
local replica_sheltered = {}

---
---@param dt idk # 
---author: 
function replica_sheltered:OnUpdate(dt)
end

---
---author: 
function replica_sheltered:CheckShade()
end

---
---author: 
function replica_sheltered:OnRemoveFromEntity()
end

---
---author: 
function replica_sheltered:StopSheltered()
end

---
---author: 
function replica_sheltered:IsSheltered()
end

---
---@param level idk # 
---author: 
function replica_sheltered:StartSheltered(level)
end

