---@meta

---@class component_slipperyfeet
local slipperyfeet = {}

---
---@param dt idk # 
---author: 
function slipperyfeet:DoDecay(dt)
end

---
---author: 
function slipperyfeet:Start_Internal()
end

---
---@param src idk # 
---@param key idk # 
---author: 
function slipperyfeet:StartSlipperySource(src,key)
end

---
---@param dt idk # 
---author: 
function slipperyfeet:OnUpdate(dt)
end

---
---author: 
function slipperyfeet:OnRemoveFromEntity()
end

---
---author: 
function slipperyfeet:GetSlipperyAndNearbyEnts()
end

---
---author: 
function slipperyfeet:OnLoad()
end

---
---author: 
function slipperyfeet:Stop_Internal()
end

---
---@param src idk # 
---@param key idk # 
---author: 
function slipperyfeet:StopSlipperySource(src,key)
end

---
---author: 
function slipperyfeet:GetDebugString()
end

---
---@param dt idk # 
---author: 
function slipperyfeet:LongUpdate(dt)
end

---
---@param reason idk # 
---author: 
function slipperyfeet:StopUpdating_Internal(reason)
end

---
---@param val idk # 
---author: 
function slipperyfeet:SetCurrent(val)
end

---
---author: 
function slipperyfeet:CalcAccumulatingSpeed()
end

---
---@param accumulating idk # 
---author: 
function slipperyfeet:SetAccumulating_Internal(accumulating)
end

---
---@param reason idk # 
---author: 
function slipperyfeet:StartUpdating_Internal(reason)
end

---
---@param delta idk # 
---author: 
function slipperyfeet:DoDelta(delta)
end

