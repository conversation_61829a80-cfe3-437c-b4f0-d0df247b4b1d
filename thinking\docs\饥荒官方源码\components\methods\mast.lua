---@meta

---@class component_mast
local mast = {}

---
---author: 
function mast:OnRemoveEntity()
end

---
---@param set idk # 
---author: 
function mast:SetReveseDeploy(set)
end

---
---@param obj idk # 
---author: 
function mast:SetRudder(obj)
end

---
---@param doer idk # 
---author: 
function mast:RemoveSailFurler(doer)
end

---
---author: 
function mast:SailUnfurled()
end

---
---@param set idk # 
---author: 
function mast:SetVelocityMod(set)
end

---
---author: 
function mast:OnRemoveFromEntity()
end

---
---author: 
function mast:CloseSail()
end

---
---author: 
function mast:UnfurlSail()
end

---
---author: 
function mast:CalcSailForce()
end

---
---@param dt idk # 
---author: 
function mast:OnUpdate(dt)
end

---
---@param doer idk # 
---@param strength idk # 
---author: 
function mast:AddSailFurler(doer,strength)
end

---
---author: 
function mast:CalcMaxVelocity()
end

---
---author: 
function mast:GetFurled0to1()
end

---
---author: 
function mast:OnDeath()
end

---
---author: 
function mast:GetCurrentFurlUnits()
end

---
---@param rudder_direction_x idk # 
---@param rudder_direction_z idk # 
---author: 
function mast:SetRudderDirection(rudder_direction_x,rudder_direction_z)
end

---
---author: 
function mast:SailFurled()
end

---
---@param set idk # 
---author: 
function mast:SetSailForce(set)
end

---
---@param boat idk # 
---author: 
function mast:SetBoat(boat)
end

