---@meta

---@class component_submersible
local submersible = {}

---
---@param x idk # 
---@param z idk # 
---@param ignore_boats idk # 
---@param nosplash idk # 
---author: 
function submersible:MakeSunken(x,z,ignore_boats,nosplash)
end

---
---author: 
function submersible:OnLanded()
end

---
---author: 
function submersible:Submerge()
end

---
---author: 
function submersible:OnSave()
end

---
---@param data idk # 
---author: 
function submersible:OnLoad(data)
end

---
---author: 
function submersible:OnRemoveFromEntity()
end

---
---author: 
function submersible:GetUnderwaterObject()
end

