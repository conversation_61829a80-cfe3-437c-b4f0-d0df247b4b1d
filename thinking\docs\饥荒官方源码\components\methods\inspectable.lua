---@meta

---@class component_inspectable
local inspectable = {}

---
---@param nameoverride idk # 
---author: 
function inspectable:SetNameOverride(nameoverride)
end

---
---@param state idk # 
---author: 
function inspectable:RecordViews(state)
end

---
---@param viewer idk # 
---author: 
function inspectable:GetDescription(viewer)
end

---
---author: 
function inspectable:OnRemoveFromEntity()
end

---
---@param viewer idk # 
---author: 
function inspectable:GetStatus(viewer)
end

