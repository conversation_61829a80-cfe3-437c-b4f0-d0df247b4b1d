---@meta

---@class component_saltlicker
local saltlicker = {}

---
---author: 
function saltlicker:Stop()
end

---
---author: 
function saltlicker:LoadPostPass()
end

---
---@param salted idk # 
---author: 
function saltlicker:SetSalted(salted)
end

---
---author: 
function saltlicker:OnSave()
end

---
---author: 
function saltlicker:GetDebugString()
end

---
---@param uses_per_lick idk # 
---author: 
function saltlicker:SetUp(uses_per_lick)
end

---
---author: 
function saltlicker:OnRemoveFromEntity()
end

