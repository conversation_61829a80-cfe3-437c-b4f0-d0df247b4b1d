---@meta

---@class component_seamlessplayerswapper
local seamlessplayerswapper = {}

---
---author: 
function seamlessplayerswapper:SwapBackToMainCharacter()
end

---
---@param old_player idk # 
---author: 
function seamlessplayerswapper:OnSeamlessCharacterSwap(old_player)
end

---
---author: 
function seamlessplayerswapper:DoMonkeyChange()
end

---
---@param data idk # 
---author: 
function seamlessplayerswapper:OnLoad(data)
end

---
---author: 
function seamlessplayerswapper:OnSave()
end

---
---@param new_prefab idk # 
---author: 
function seamlessplayerswapper:_StartSwap(new_prefab)
end

---
---author: 
function seamlessplayerswapper:SaveForReroll()
end

---
---author: 
function seamlessplayerswapper:PostTransformSetup()
end

