---@meta

---@class widget_resurrectbutton: widget_widget
---@overload fun(owner:idk): widget_resurrectbutton
---@field _ctor function #
---@field owner idk #
---@field hud_focus idk #
---@field button idk #
---@field text idk #
local resurrectbutton = {}

---
---@param focus idk #
---
---author: 
function resurrectbutton:ToggleHUDFocus(focus) end

---
---@param pos idk #
---@param y idk #
---@param z idk #
---
---author: 
function resurrectbutton:SetScale(pos, y, z) end

---
---
---author: 
function resurrectbutton:OnShow() end

---
---@param control idk #
---@param down idk #
---
---author: 
function resurrectbutton:CheckControl(control, down) end

---
---
---author: 
function resurrectbutton:DoResurrect() end

