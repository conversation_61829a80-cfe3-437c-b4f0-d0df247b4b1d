---@meta

---@class component_playervision
local playervision = {}

---
---author: 
function playervision:GetCCPhaseFn()
end

---
---author: 
function playervision:HasRoseGlassesVision()
end

---
---author: 
function playervision:HasGoggleVision()
end

---
---author: 
function playervision:HasScrapMonoleVision()
end

---
---@param force idk # 
---author: 
function playervision:ForceRoseGlassesVision(force)
end

---
---author: 
function playervision:HasNightmareVision()
end

---
---@param enabled idk # 
---author: 
function playervision:SetGhostVision(enabled)
end

---
---@param cctable idk # 
---@param blend idk # 
---author: 
function playervision:SetCustomCCTable(cctable,blend)
end

---
---author: 
function playervision:UpdateCCTable()
end

---
---author: 
function playervision:HasGhostVision()
end

---
---@param source idk # 
---@param priority idk # 
---@param customcctable idk # 
---@param blend idk # 
---author: 
function playervision:PushForcedNightVision(source,priority,customcctable,blend)
end

---
---@param force idk # 
---author: 
function playervision:ForceInspectaclesVision(force)
end

---
---@param force idk # 
---author: 
function playervision:ForceScrapMonoleVision(force)
end

---
---author: 
function playervision:HasNightVision()
end

---
---@param enabled idk # 
---author: 
function playervision:SetNightmareVision(enabled)
end

---
---author: 
function playervision:HasNutrientsVision()
end

---
---@param force idk # 
---author: 
function playervision:ForceNutrientVision(force)
end

---
---@param force idk # 
---author: 
function playervision:ForceGoggleVision(force)
end

---
---@param source idk # 
---author: 
function playervision:PopForcedNightVision(source)
end

---
---@param force idk # 
---author: 
function playervision:ForceNightVision(force)
end

---
---author: 
function playervision:GetCCTable()
end

---
---author: 
function playervision:HasInspectaclesVision()
end

