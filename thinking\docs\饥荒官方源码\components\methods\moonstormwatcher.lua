---@meta

---@class component_moonstormwatcher
local moonstormwatcher = {}

---
---author: 
function moonstormwatcher:UpdateMoonstormLevel()
end

---
---@param mult idk # 
---author: 
function moonstormwatcher:SetMoonstormSpeedMultiplier(mult)
end

---
---@param level idk # 
---author: 
function moonstormwatcher:UpdateMoonstormWalkSpeed_Internal(level)
end

---
---author: 
function moonstormwatcher:GetMoonStormLevel()
end

---
---@param data idk # 
---author: 
function moonstormwatcher:ToggleMoonstorms(data)
end

---
---author: 
function moonstormwatcher:OnRemoveFromEntity()
end

---
---author: 
function moonstormwatcher:UpdateMoonstormWalkSpeed()
end

