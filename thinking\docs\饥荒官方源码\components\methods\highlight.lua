---@meta

---@class component_highlight
local highlight = {}

---
---@param toadd idk # 
---@param timein idk # 
---@param timeout idk # 
---author: 
function highlight:Flash(toadd,timein,timeout)
end

---
---@param col idk # 
---author: 
function highlight:SetAddColour(col)
end

---
---author: 
function highlight:OnRemoveFromEntity()
end

---
---@param dt idk # 
---author: 
function highlight:OnUpdate(dt)
end

---
---@param r idk # 
---@param g idk # 
---@param b idk # 
---author: 
function highlight:Highlight(r,g,b)
end

---
---author: 
function highlight:ApplyColour()
end

---
---author: 
function highlight:UnHighlight()
end

