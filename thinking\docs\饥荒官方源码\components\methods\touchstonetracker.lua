---@meta

---@class component_touchstonetracker
local touchstonetracker = {}

---
---@param newinst idk # 
---author: 
function touchstonetracker:TransferComponent(newinst)
end

---
---@param data idk # 
---author: 
function touchstonetracker:OnLoad(data)
end

---
---author: 
function touchstonetracker:OnSave()
end

---
---author: 
function touchstonetracker:GetDebugString()
end

---
---author: 
function touchstonetracker:OnRemoveFromEntity()
end

---
---@param touchstone idk # 
---author: 
function touchstonetracker:IsUsed(touchstone)
end

