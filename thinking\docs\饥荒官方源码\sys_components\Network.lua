---@meta

---@class Network
local Network = {}

---
---UNKNOWN
---
---@param ... any
---author:
function Network:GetUserID(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:IsServerAdmin(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:GetPlayerColour(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:IsPlayingWithFriends(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:SetPlayerSkin(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:SetPlayerAge(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:SetClassifiedTarget(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:SetPlayerSkillSelection(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:GetUserFlags(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:IsBorrowed(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:GetClientName(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:AddUserFlag(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:SetPlayerEquip(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:GetPlayerAge(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:GetNetworkID(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:SetConsecutiveMatch(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:RemoveUserFlag(...) end

---
---UNKNOWN
---
---@param ... any
---author:
function Network:IsConsecutiveMatch(...) end

