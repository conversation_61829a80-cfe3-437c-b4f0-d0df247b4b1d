---@meta

---@class component_pethealthbar
local pethealthbar = {}

---
---author: 
function pethealthbar:GetOverTime()
end

---
---author: 
function pethealthbar:ResetPulse()
end

---
---author: 
function pethealthbar:GetPercent()
end

---
---author: 
function pethealthbar:GetSymbol()
end

---
---@param petskin idk # 
---author: 
function pethealthbar:SetPetSkin(petskin)
end

---
---author: 
function pethealthbar:GetPulse()
end

---
---author: 
function pethealthbar:GetDebugString()
end

---
---author: 
function pethealthbar:GetMaxHealth()
end

---
---@param dt idk # 
---author: 
function pethealthbar:OnUpdate(dt)
end

---
---@param pet idk # 
---@param symbol idk # 
---@param maxhealth idk # 
---author: 
function pethealthbar:SetPet(pet,symbol,maxhealth)
end

---
---@param symbol idk # 
---author: 
function pethealthbar:SetSymbol(symbol)
end

---
---@param max_health idk # 
---author: 
function pethealthbar:SetMaxHealth(max_health)
end

