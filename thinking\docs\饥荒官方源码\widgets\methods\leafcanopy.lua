---@meta

---@class widget_leafcanopy: widget_widget
---@overload fun(owner:idk): widget_leafcanopy
---@field _ctor function #
---@field owner idk #
---@field leavestop_intensity idk #
---@field leavespercent idk #
---@field under_leaves idk #
---@field leavesfullyin idk #
---@field lastframecoords idk #
local leafcanopy = {}

---
---@param dt idk #
---
---author: 
function leafcanopy:OnUpdate(dt) end

