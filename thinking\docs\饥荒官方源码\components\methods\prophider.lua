---@meta

---@class component_prophider
local prophider = {}

---
---author: 
function prophider:GenerateHideTime()
end

---
---@param fn idk # 
---author: 
function prophider:SetOnVisibleFn(fn)
end

---
---@param data idk # 
---author: 
function prophider:OnLoad(data)
end

---
---@param fn idk # 
---author: 
function prophider:SetOnUnhideFn(fn)
end

---
---@param fn idk # 
---author: 
function prophider:SetWillUnhideFn(fn)
end

---
---author: 
function prophider:GetDebugString()
end

---
---author: 
function prophider:OnSave()
end

---
---author: 
function prophider:ShowFromProp()
end

---
---@param fn idk # 
---author: 
function prophider:SetPropCreationFn(fn)
end

---
---author: 
function prophider:OnEntityWake()
end

---
---@param duration idk # 
---@param counter idk # 
---author: 
function prophider:HideWithProp(duration,counter)
end

---
---@param fn idk # 
---author: 
function prophider:SetOnHideFn(fn)
end

---
---author: 
function prophider:ClearHideTask()
end

