---@meta

---@class component_witherable
local witherable = {}

---
---author: 
function witherable:CanRejuvenate()
end

---
---author: 
function witherable:ForceWither()
end

---
---author: 
function witherable:GetDebugString()
end

---
---@param data idk # 
---author: 
function witherable:OnLoad(data)
end

---
---@param delay idk # 
---author: 
function witherable:DelayWither(delay)
end

---
---author: 
function witherable:IsProtected()
end

---
---author: 
function witherable:Start()
end

---
---author: 
function witherable:CanWither()
end

---
---author: 
function witherable:Stop()
end

---
---author: 
function witherable:ForceRejuvenate()
end

---
---@param delay idk # 
---author: 
function witherable:DelayRejuvenate(delay)
end

---
---author: 
function witherable:OnSave()
end

---
---author: 
function witherable:IsWithered()
end

---
---author: 
function witherable:OnRemoveFromEntity()
end

---
---@param enable idk # 
---author: 
function witherable:Enable(enable)
end

