---@meta

---@class component_playeravatardata
local playeravatardata = {}

---
---@param allow idk # 
---author: 
function playeravatardata:SetAllowEmptyName(allow)
end

---
---@param allow idk # 
---author: 
function playeravatardata:SetAllowBurnt(allow)
end

---
---@param data idk # 
---author: 
function playeravatardata:OnLoad(data)
end

---
---@param client_obj idk # 
---author: 
function playeravatardata:SetData(client_obj)
end

---
---@param save idk # 
---author: 
function playeravatardata:AddAgeData(save)
end

---
---@param save idk # 
---author: 
function playeravatardata:AddBaseSkinData(save)
end

---
---@param save idk # 
---author: 
function playeravatardata:AddPlayerData(save)
end

---
---author: 
function playeravatardata:GetData()
end

---
---author: 
function playeravatardata:OnSave()
end

---
---@param save idk # 
---author: 
function playeravatardata:AddEquipData(save)
end

---
---@param save idk # 
---author: 
function playeravatardata:AddNameData(save)
end

---
---@param save idk # 
---author: 
function playeravatardata:AddClothingData(save)
end

