---@meta

---@class component_possessedaxe
local possessedaxe = {}

---
---author: 
function possessedaxe:GetDebugString()
end

---
---author: 
function possessedaxe:Drop()
end

---
---author: 
function possessedaxe:StopWaitingForPlayer()
end

---
---author: 
function possessedaxe:OnSave()
end

---
---author: 
function possessedaxe:Revert()
end

---
---@param data idk # 
---author: 
function possessedaxe:OnLoad(data)
end

---
---@param userid idk # 
---@param delay idk # 
---author: 
function possessedaxe:WaitForPlayer(userid,delay)
end

---
---@param player idk # 
---author: 
function possessedaxe:LinkToPlayer(player)
end

