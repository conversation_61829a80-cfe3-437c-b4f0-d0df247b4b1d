---@meta

---@class replica_sanity
local replica_sanity = {}

---
---@param ratescale idk # 
---author: 
function replica_sanity:SetRateScale(ratescale)
end

---
---author: 
function replica_sanity:IsInsanityMode()
end

---
---@param sane idk # 
---author: 
function replica_sanity:SetIsSane(sane)
end

---
---@param penalty idk # 
---author: 
function replica_sanity:SetPenalty(penalty)
end

---
---author: 
function replica_sanity:OnRemoveFromEntity()
end

---
---author: 
function replica_sanity:DetachClassified()
end

---
---@param classified idk # 
---author: 
function replica_sanity:AttachClassified(classified)
end

---
---author: 
function replica_sanity:GetPercentNetworked()
end

---
---author: 
function replica_sanity:MaxWithPenalty()
end

---
---author: 
function replica_sanity:GetSanityMode()
end

---
---author: 
function replica_sanity:Max()
end

---
---author: 
function replica_sanity:GetPercentWithPenalty()
end

---
---author: 
function replica_sanity:GetPercent()
end

---
---author: 
function replica_sanity:IsLunacyMode()
end

---
---author: 
function replica_sanity:IsSane()
end

---
---author: 
function replica_sanity:IsGhostDrain()
end

---
---author: 
function replica_sanity:GetPenaltyPercent()
end

---
---author: 
function replica_sanity:GetCurrent()
end

---
---author: 
function replica_sanity:IsEnlightened()
end

---
---author: 
function replica_sanity:IsInsane()
end

---
---@param mode idk # 
---author: 
function replica_sanity:SetSanityMode(mode)
end

---
---@param current idk # 
---author: 
function replica_sanity:SetCurrent(current)
end

---
---author: 
function replica_sanity:GetRateScale()
end

---
---author: 
function replica_sanity:IsCrazy()
end

---
---@param ghostdrainmult idk # 
---author: 
function replica_sanity:SetGhostDrainMult(ghostdrainmult)
end

---
---@param max idk # 
---author: 
function replica_sanity:SetMax(max)
end

