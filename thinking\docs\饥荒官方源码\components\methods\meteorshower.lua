---@meta

---@class component_meteorshower
local meteorshower = {}

---
---@param data idk # 
---author: 
function meteorshower:OnLoad(data)
end

---
---@param level idk # 
---author: 
function meteorshower:StartShower(level)
end

---
---author: 
function meteorshower:IsCoolingDown()
end

---
---@param mod idk # 
---author: 
function meteorshower:SpawnMeteor(mod)
end

---
---author: 
function meteorshower:GetDebugString()
end

---
---author: 
function meteorshower:OnSave()
end

---
---author: 
function meteorshower:StartCooldown()
end

---
---author: 
function meteorshower:StopShower()
end

---
---author: 
function meteorshower:IsShowering()
end

