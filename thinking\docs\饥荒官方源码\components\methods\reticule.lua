---@meta

---@class component_reticule
local reticule = {}

---
---author: 
function reticule:Blip()
end

---
---author: 
function reticule:ClearTwinStickOverrides()
end

---
---author: 
function reticule:ShouldHide()
end

---
---author: 
function reticule:UpdateTwinStickMode2()
end

---
---author: 
function reticule:CreateReticule()
end

---
---author: 
function reticule:DestroyReticule()
end

---
---author: 
function reticule:UpdateTwinStickMode1()
end

---
---@param dt idk # 
---author: 
function reticule:OnCameraUpdate(dt)
end

---
---@param dt idk # 
---author: 
function reticule:OnUpdate(dt)
end

---
---@param dt idk # 
---author: 
function reticule:UpdatePosition(dt)
end

---
---@param pos idk # 
---author: 
function reticule:PingReticuleAt(pos)
end

---
---author: 
function reticule:UpdateColour()
end

