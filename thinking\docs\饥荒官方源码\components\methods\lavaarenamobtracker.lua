---@meta

---@class component_lavaarenamobtracker
local lavaarenamobtracker = {}

---
---@param ent idk # 
---author: 
function lavaarenamobtracker:StartTracking(ent)
end

---
---author: 
function lavaarenamobtracker:GetNumMobs()
end

---
---@param cb idk # 
---@param params idk # 
---author: 
function lavaarenamobtracker:ForEachMob(cb,params)
end

---
---@param x idk # 
---@param y idk # 
---@param z idk # 
---@param r idk # 
---@param musttags idk # 
---@param canttags idk # 
---@param mustoneoftags idk # 
---author: 
function lavaarenamobtracker:FindMobs(x,y,z,r,musttags,canttags,mustoneoftags)
end

---
---@param ent idk # 
---author: 
function lavaarenamobtracker:StopTracking(ent)
end

---
---author: 
function lavaarenamobtracker:GetAllMobs()
end

