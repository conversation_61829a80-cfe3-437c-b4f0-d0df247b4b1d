---@meta

---@class component_pinnable
local pinnable = {}

---
---author: 
function pinnable:IsValidPinTarget()
end

---
---@param wearofftime idk # 
---author: 
function pinnable:SetDefaultWearOffTime(wearofftime)
end

---
---author: 
function pinnable:IsStuck()
end

---
---@param ratio idk # 
---author: 
function pinnable:SpawnShatterFX(ratio)
end

---
---author: 
function pinnable:RemainingRatio()
end

---
---@param wearofftime idk # 
---author: 
function pinnable:StartWearingOff(wearofftime)
end

---
---author: 
function pinnable:Unstick()
end

---
---@param goo_build idk # 
---@param splashfxlist idk # 
---author: 
function pinnable:Stick(goo_build,splashfxlist)
end

---
---author: 
function pinnable:OnRemoveFromEntity()
end

---
---author: 
function pinnable:UpdateStuckStatus()
end

