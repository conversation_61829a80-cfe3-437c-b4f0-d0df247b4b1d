---@meta

---@class replica_health
local replica_health = {}

---
---author: 
function replica_health:IsTakingFireDamage()
end

---
---@param penalty idk # 
---author: 
function replica_health:SetPenalty(penalty)
end

---
---author: 
function replica_health:OnRemoveFromEntity()
end

---
---author: 
function replica_health:DetachClassified()
end

---
---@param classified idk # 
---author: 
function replica_health:AttachClassified(classified)
end

---
---@param canheal idk # 
---author: 
function replica_health:SetCanHeal(canheal)
end

---
---author: 
function replica_health:MaxWithPenalty()
end

---
---author: 
function replica_health:IsTakingFireDamageLow()
end

---
---@param istakingfiredamage idk # 
---author: 
function replica_health:SetIsTakingFireDamage(istakingfiredamage)
end

---
---author: 
function replica_health:GetPercent()
end

---
---author: 
function replica_health:Max()
end

---
---author: 
function replica_health:CanHeal()
end

---
---@param canmurder idk # 
---author: 
function replica_health:SetCanMurder(canmurder)
end

---
---author: 
function replica_health:CanMurder()
end

---
---author: 
function replica_health:GetCurrent()
end

---
---@param istakingfiredamagelow idk # 
---author: 
function replica_health:SetIsTakingFireDamageLow(istakingfiredamagelow)
end

---
---@param isdead idk # 
---author: 
function replica_health:SetIsDead(isdead)
end

---
---author: 
function replica_health:IsDead()
end

---
---@param current idk # 
---author: 
function replica_health:SetCurrent(current)
end

---
---author: 
function replica_health:IsHurt()
end

---
---author: 
function replica_health:GetPenaltyPercent()
end

---
---author: 
function replica_health:IsTakingFireDamageFull()
end

---
---@param max idk # 
---author: 
function replica_health:SetMax(max)
end

