---@meta

---@class component_transparentonsanity
local transparentonsanity = {}

---
---author: 
function transparentonsanity:CalcaulteTargetAlpha()
end

---
---author: 
function transparentonsanity:ForceUpdate()
end

---
---@param dt idk # 
---@param force idk # 
---author: 
function transparentonsanity:DoUpdate(dt,force)
end

---
---author: 
function transparentonsanity:OnEntityWake()
end

---
---author: 
function transparentonsanity:GetDebugString()
end

---
---@param dt idk # 
---author: 
function transparentonsanity:OnUpdate(dt)
end

---
---author: 
function transparentonsanity:OnEntitySleep()
end

