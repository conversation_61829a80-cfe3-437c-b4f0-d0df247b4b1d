---@meta

---@class component_leader
local leader = {}

---
---author: 
function leader:RemoveAllFollowersOnDeath()
end

---
---@param target idk # 
---author: 
function leader:OnNew<PERSON>arget(target)
end

---
---@param newents idk # 
---@param savedata idk # 
---author: 
function leader:LoadPostPass(newents,savedata)
end

---
---@param guy idk # 
---author: 
function leader:<PERSON><PERSON><PERSON><PERSON><PERSON>(guy)
end

---
---@param target idk # 
---author: 
function leader:IsTargetedByFollowers(target)
end

---
---author: 
function leader:GetDebugString()
end

---
---author: 
function leader:RemoveAllFollow<PERSON>()
end

---
---@param attacker idk # 
---author: 
function leader:OnAttacked(attacker)
end

---
---author: 
function leader:OnSave()
end

---
---@param prefabName idk # 
---author: 
function leader:IsBeingFollowedBy(prefabName)
end

---
---author: 
function leader:HaveFoll<PERSON><PERSON>CachePlayer<PERSON>eader()
end

---
---@param follower idk # 
---author: 
function leader:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(follower)
end

---
---@param tag idk # 
---@param validateremovefn idk # 
---author: 
function leader:RemoveFollowersByTag(tag,validateremovefn)
end

---
---@param follower idk # 
---@param invalid idk # 
---author: 
function leader:RemoveFollower(follower,invalid)
end

---
---author: 
function leader:OnRemoveFromEntity()
end

---
---@param tag idk # 
---author: 
function leader:CountFollowers(tag)
end

