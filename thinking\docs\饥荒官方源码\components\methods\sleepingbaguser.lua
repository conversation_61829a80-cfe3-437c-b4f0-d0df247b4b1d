---@meta

---@class component_sleepingbaguser
local sleepingbaguser = {}

---
---@param bed idk # 
---author: 
function sleepingbaguser:Do<PERSON>leep(bed)
end

---
---@param bonus idk # 
---author: 
function sleepingbaguser:SetHungerBonusMult(bonus)
end

---
---author: 
function sleepingbaguser:SleepTick()
end

---
---@param bonus idk # 
---author: 
function sleepingbaguser:SetHealthBonusMult(bonus)
end

---
---author: 
function sleepingbaguser:ShouldSleep()
end

---
---@param nostatechange idk # 
---author: 
function sleepingbaguser:DoWakeUp(nostatechange)
end

---
---@param cansleepfn idk # 
---author: 
function sleepingbaguser:SetCanSleepFn(cansleepfn)
end

---
---@param bonus idk # 
---author: 
function sleepingbaguser:SetSanityBonusMult(bonus)
end

