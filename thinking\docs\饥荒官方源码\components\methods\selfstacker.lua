---@meta

---@class component_selfstacker
local selfstacker = {}

---
---author: 
function selfstacker:OnRemoveEntity()
end

---
---author: 
function selfstacker:CanSelfStack()
end

---
---@param ignorespeedcheck idk # 
---author: 
function selfstacker:SetIgnoreMovingFast(ignorespeedcheck)
end

---
---author: 
function selfstacker:OnEntityWake()
end

---
---author: 
function selfstacker:DoStack()
end

---
---author: 
function selfstacker:FindItemToStackWith()
end

