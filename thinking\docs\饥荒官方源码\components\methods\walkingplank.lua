---@meta

---@class component_walkingplank
local walkingplank = {}

---
---author: 
function walkingplank:StopMounting()
end

---
---author: 
function walkingplank:Retract()
end

---
---author: 
function walkingplank:Extend()
end

---
---@param doer idk # 
---author: 
function walkingplank:AbandonShip(doer)
end

---
---author: 
function walkingplank:OnRemoveFromEntity()
end

---
---@param doer idk # 
---author: 
function walkingplank:MountPlank(doer)
end

