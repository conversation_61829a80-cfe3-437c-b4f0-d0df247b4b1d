---@meta

---@class widget_skinspuppet: widget_button
---@overload fun(emote_min_time:idk, emote_max_time:idk): widget_skinspuppet
---@field _ctor function #
---@field emote_min_time idk #
---@field emote_max_time idk #
---@field anim idk #
---@field animstate idk #
---@field currentanimbank idk #
---@field current_idle_anim idk #
---@field default_build idk #
---@field last_skins idk #
---@field enable_idle_emotes idk #
---@field time_to_change_emote idk #
---@field queued_change_slot idk #
---@field play_non_idle_emotes idk #
---@field add_change_emote_for_idle idk #
---@field sitting idk #
---@field shadow idk #
---@field looping idk #
---@field prefabname idk #
---@field override_build idk #
---@field item_equip idk #
---@field time_to_idle_emote idk #
---@field current_skinmode idk #
---@field scarecrow_pose idk #
---@field beard_length idk #
---@field beard idk #
---@field character idk #
local skinspuppet = {}

---
---
---author: 
function skinspuppet:AddShadow() end

---
---@param emote idk #
---@param loop idk #
---@param force idk #
---@param do_push idk #
---
---author: 
function skinspuppet:DoEmote(emote, loop, force, do_push) end

---
---
---author: 
function skinspuppet:Sit() end

---
---
---author: 
function skinspuppet:DoIdleEmote() end

---
---
---author: 
function skinspuppet:DoChangeEmote() end

---
---
---author: 
function skinspuppet:_ResetIdleEmoteTimer() end

---
---
---author: 
function skinspuppet:RemoveEquipped() end

---
---@param dt idk #
---
---author: 
function skinspuppet:EmoteUpdate(dt) end

---
---@param character idk #
---
---author: 
function skinspuppet:SetCharacter(character) end

---
---@param prefabname idk #
---@param base_item idk #
---@param clothing_names idk #
---@param skip_change_emote idk #
---@param skinmode idk #
---@param monkey_curse idk #
---
---author: 
function skinspuppet:SetSkins(prefabname, base_item, clothing_names, skip_change_emote, skinmode, monkey_curse) end

---
---@param length idk #
---
---author: 
function skinspuppet:SetBeardLength(length) end

---
---@param beard idk #
---
---author: 
function skinspuppet:SetBeard(beard) end

---
---
---author: 
function skinspuppet:OnGainFocus() end

