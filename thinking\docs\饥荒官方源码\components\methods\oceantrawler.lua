---@meta

---@class component_oceantrawler
local oceantrawler = {}

---
---author: 
function oceantrawler:HasFishEscaped()
end

---
---author: 
function oceantrawler:Raise()
end

---
---author: 
function oceantrawler:StartUpdate()
end

---
---author: 
function oceantrawler:OnEntityWake()
end

---
---author: 
function oceantrawler:OnEntitySleep()
end

---
---author: 
function oceantrawler:LoadPostPass()
end

---
---@param eater idk # 
---author: 
function oceantrawler:GetBait(eater)
end

---
---author: 
function oceantrawler:Lower()
end

---
---@param dt idk # 
---author: 
function oceantrawler:OnUpdate(dt)
end

---
---@param data idk # 
---author: 
function oceantrawler:OnLoad(data)
end

---
---author: 
function oceantrawler:SimulateCatchFish()
end

---
---author: 
function oceantrawler:StopUpdating()
end

---
---@param spawnpoint idk # 
---author: 
function oceantrawler:GetOceanTrawlerSpawnChanceModifier(spawnpoint)
end

---
---author: 
function oceantrawler:CheckForMalbatross()
end

---
---author: 
function oceantrawler:Fix()
end

---
---author: 
function oceantrawler:Reset()
end

---
---author: 
function oceantrawler:OnSave()
end

---
---author: 
function oceantrawler:ReleaseOverflowFish()
end

---
---author: 
function oceantrawler:HasCaughtItem()
end

---
---author: 
function oceantrawler:IsLowered()
end

