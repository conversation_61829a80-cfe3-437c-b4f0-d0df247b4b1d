---@meta

---@class component_wereness
local wereness = {}

---
---@param percent idk # 
---@param overtime idk # 
---author: 
function wereness:SetPercent(percent,overtime)
end

---
---@param delta idk # 
---@param overtime idk # 
---author: 
function wereness:DoD<PERSON><PERSON>(delta,overtime)
end

---
---author: 
function wereness:GetDebugString()
end

---
---@param data idk # 
---author: 
function wereness:OnLoad(data)
end

---
---author: 
function wereness:GetWereMode()
end

---
---author: 
function wereness:OnSave()
end

---
---author: 
function wereness:StartDraining()
end

---
---@param dt idk # 
---author: 
function wereness:OnUpdate(dt)
end

---
---@param weremode idk # 
---author: 
function wereness:SetWereMode(weremode)
end

---
---author: 
function wereness:StopDraining()
end

---
---@param rate idk # 
---author: 
function wereness:SetDrainRate(rate)
end

---
---author: 
function wereness:GetPercent()
end

