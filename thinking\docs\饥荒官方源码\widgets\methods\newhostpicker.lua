---@meta

---@class widget_newhostpicker: widget_widget
---@overload fun(): widget_newhostpicker
---@field _ctor function #
---@field buttons idk #
---@field headertext idk #
---@field description idk #
---@field cb idk #
---@field next_focus idk #
local newhostpicker = {}

---
---@param cb idk #
---
---author: 
function newhostpicker:SetCallback(cb) end

---
---@param intention idk #
---
---author: 
function newhostpicker:SetSelected(intention) end

---
---@param direction idk #
---
---author: 
function newhostpicker:SetFocus(direction) end

