---@meta

---@class component_wereeater
local wereeater = {}

---
---author: 
function wereeater:ResetFoodMemory()
end

---
---author: 
function wereeater:GetDebugString()
end

---
---@param data idk # 
---author: 
function wereeater:OnLoad(data)
end

---
---@param data idk # 
---author: 
function wereeater:EatMosterFood(data)
end

---
---author: 
function wereeater:OnSave()
end

---
---@param fn idk # 
---author: 
function wereeater:SetForceTransformFn(fn)
end

---
---author: 
function wereeater:OnRemoveFromEntity()
end

---
---@param mode idk # 
---author: 
function wereeater:ForceTransformToWere(mode)
end

