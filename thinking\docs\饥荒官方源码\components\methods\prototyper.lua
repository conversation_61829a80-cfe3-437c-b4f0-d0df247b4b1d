---@meta

---@class component_prototyper
local prototyper = {}

---
---@param doer idk # 
---author: 
function prototyper:TurnOn(doer)
end

---
---@param doer idk # 
---author: 
function prototyper:TurnOff(doer)
end

---
---@param doer idk # 
---@param recipe idk # 
---author: 
function prototyper:Activate(doer,recipe)
end

---
---author: 
function prototyper:OnRemoveFromEntity()
end

---
---author: 
function prototyper:GetTechTrees()
end

