---@meta

---@class component_tributable
local tributable = {}

---
---@param data idk # 
---author: 
function tributable:OnLoad(data)
end

---
---author: 
function tributable:HasPendingReward()
end

---
---author: 
function tributable:OnSave()
end

---
---author: 
function tributable:GetDebugString()
end

---
---author: 
function tributable:OnRefuse()
end

---
---@param value idk # 
---@param tributer idk # 
---author: 
function tributable:OnAccept(value,tributer)
end

