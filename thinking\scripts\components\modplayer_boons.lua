-- Component files also need GLO<PERSON>L declaration in DST when accessing global variables
-- 确保能访问全局变量
local GLOBAL = rawget(_G, "GLOBAL") or _G

local DEGREES = GLOBAL.DEGREES or (math.pi / 180)
local GROUND = GLOBAL.GROUND or {}
local GetTime = GLOBAL.GetTime or function() return 0 end
local SpawnPrefab = GLOBAL.SpawnPrefab or function() return nil end
local TheWorld = GLOBAL.TheWorld or {Map = {GetTileAtPoint = function() return 1 end}}

-- 被动恩惠定义 - 按职业分类
local BOON_DEFINITIONS = {
    -- === 战士系 ===
    warrior_speed = {
        name = "战士疾行",
        desc = "移动速度提升20%",
        cost = 25,
        max_level = 1,
        category = "warrior",
        effect_type = "speed",
        effect_value = 1.2
    },
    warrior_damage = {
        name = "战士之力",
        desc = "攻击力提升30%",
        cost = 35,
        max_level = 1,
        category = "warrior",
        effect_type = "damage_boost",
        effect_value = 1.3
    },
    warrior_combo = {
        name = "连击专精",
        desc = "连续攻击同一目标时伤害递增(最多5层)",
        cost = 50,
        max_level = 1,
        category = "warrior",
        effect_type = "combo_system",
        effect_value = 5
    },

    -- === 法师系 ===
    mage_poison = {
        name = "毒素掌控",
        desc = "攻击附带毒伤，每秒造成10点伤害，持续5秒",
        cost = 40,
        max_level = 1,
        category = "mage",
        effect_type = "poison_damage",
        effect_value = {damage = 10, duration = 5}
    },
    mage_charge = {
        name = "蓄力打击",
        desc = "长按攻击键蓄力，最多增加200%伤害",
        cost = 60,
        max_level = 1,
        category = "mage",
        effect_type = "charge_attack",
        effect_value = 3.0
    },
    mage_teleport = {
        name = "闪现术",
        desc = "双击移动键瞬移8格距离(冷却30秒)",
        cost = 80,
        max_level = 1,
        category = "mage",
        effect_type = "teleport_skill",
        effect_value = {distance = 8, cooldown = 30}
    },

    -- === 召唤师系 ===
    summon_spider = {
        name = "蜘蛛召唤",
        desc = "召唤友好蜘蛛协助战斗(最多2只)",
        cost = 45,
        max_level = 1,
        category = "summoner",
        effect_type = "summon_creature",
        effect_value = {prefab = "spider_warrior", max_count = 2, duration = 120}
    },
    summon_pig = {
        name = "猪人护卫",
        desc = "召唤猪人护卫跟随(最多1只)",
        cost = 70,
        max_level = 1,
        category = "summoner",
        effect_type = "summon_creature",
        effect_value = {prefab = "pigman", max_count = 1, duration = 180}
    },
    summon_boss = {
        name = "巨兽召唤",
        desc = "召唤小型巨鹿协助战斗(最多1只)",
        cost = 120,
        max_level = 1,
        category = "summoner",
        effect_type = "summon_creature",
        effect_value = {prefab = "deerclops", max_count = 1, duration = 60, scale = 0.5}
    },

    -- === 农民系 ===
    farmer_growth = {
        name = "绿拇指",
        desc = "玩家周围8格范围内植物生长速度加快100%",
        cost = 30,
        max_level = 1,
        category = "farmer",
        effect_type = "plant_growth",
        effect_value = {range = 8, growth_multiplier = 2.0, check_interval = 10}
    },
    farmer_harvest = {
        name = "自动收获",
        desc = "玩家周围1格范围内成熟作物自动收获",
        cost = 55,
        max_level = 1,
        category = "farmer",
        effect_type = "auto_harvest",
        effect_value = {range = 1, check_interval = 5}
    },
    farmer_blessing = {
        name = "丰收祝福",
        desc = "采集时恢复饥饿+10、理智+5、生命+3",
        cost = 75,
        max_level = 1,
        category = "farmer",
        effect_type = "harvest_blessing",
        effect_value = {hunger = 10, sanity = 5, health = 3}
    }
}

local ModPlayerBoons = Class(function(self, inst)
    self.inst = inst
    self.favor = 0
    self.unlocked_boons = {}  -- {boon_id = level}
    self.equipped_boons = {}  -- [slot1, slot2] 最多装备2个
    self.max_equipped = 2
    self.active_effects = {}  -- 当前激活的效果清理函数

    -- 战士系统相关
    self.combo_stacks = {}  -- {target_guid = stack_count}
    self.combo_timers = {}  -- {target_guid = timer_task}

    -- 法师系统相关
    self.charge_start_time = 0
    self.is_charging = false
    self.teleport_cooldown = 0
    self.last_move_time = 0
    self.move_key_count = 0

    -- 召唤师系统相关
    self.summoned_creatures = {}  -- {boon_id = {creature1, creature2, ...}}
end)

function ModPlayerBoons:AddFavor(amount)
    self.favor = self.favor + amount
    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "获得", amount, "灵韵，总计:", self.favor)

    if self.inst.components.talker then
        self.inst.components.talker:Say(string.format("获得 %d 灵韵！(总计: %d)", amount, self.favor))
    end
end

-- 获取被动恩惠定义
function ModPlayerBoons:GetBoonDefinition(boon_id)
    return BOON_DEFINITIONS[boon_id]
end

-- 获取所有可用的被动恩惠
function ModPlayerBoons:GetAvailableBoons()
    return BOON_DEFINITIONS
end

-- 检查是否可以购买被动恩惠
function ModPlayerBoons:CanUnlockBoon(boon_id)
    local boon_def = BOON_DEFINITIONS[boon_id]
    if not boon_def then return false, "未知的被动恩惠" end

    local current_level = self.unlocked_boons[boon_id] or 0
    if current_level >= boon_def.max_level then
        return false, "已达到最大等级"
    end

    if self.favor < boon_def.cost then
        return false, string.format("灵韵不足，需要%d", boon_def.cost)
    end

    return true, "可以购买"
end

-- 购买/解锁被动恩惠
function ModPlayerBoons:UnlockBoon(boon_id, silent)
    local can_unlock, reason = self:CanUnlockBoon(boon_id)
    if not can_unlock then
        if not silent and self.inst.components.talker then
            self.inst.components.talker:Say(reason)
        end
        return false
    end

    local boon_def = BOON_DEFINITIONS[boon_id]
    self.favor = self.favor - boon_def.cost
    self.unlocked_boons[boon_id] = (self.unlocked_boons[boon_id] or 0) + 1

    if not silent and self.inst.components.talker then
        self.inst.components.talker:Say(string.format("✓ 解锁了 %s！", boon_def.name))
    end

    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "解锁被动恩惠:", boon_def.name)
    return true
end

-- 检查是否可以装备被动恩惠
function ModPlayerBoons:CanEquipBoon(boon_id)
    if not self.unlocked_boons[boon_id] then
        return false, "尚未解锁此被动恩惠"
    end

    -- 检查是否已经装备
    for _, equipped_id in ipairs(self.equipped_boons) do
        if equipped_id == boon_id then
            return false, "已经装备此被动恩惠"
        end
    end

    if #self.equipped_boons >= self.max_equipped then
        return false, string.format("装备槽已满（%d/%d）", #self.equipped_boons, self.max_equipped)
    end

    return true, "可以装备"
end

-- 装备被动恩惠
function ModPlayerBoons:EquipBoon(boon_id, silent)
    local can_equip, reason = self:CanEquipBoon(boon_id)
    if not can_equip then
        if not silent and self.inst.components.talker then
            self.inst.components.talker:Say(reason)
        end
        return false
    end

    table.insert(self.equipped_boons, boon_id)
    self:ApplyBoonEffect(boon_id)

    local boon_def = BOON_DEFINITIONS[boon_id]
    if not silent and self.inst.components.talker then
        self.inst.components.talker:Say(string.format("✓ 装备了 %s", boon_def.name))
    end

    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "装备被动恩惠:", boon_def.name)
    return true
end

-- 卸下被动恩惠
function ModPlayerBoons:UnequipBoon(boon_id, silent)
    for i, equipped_id in ipairs(self.equipped_boons) do
        if equipped_id == boon_id then
            table.remove(self.equipped_boons, i)
            self:RemoveBoonEffect(boon_id)

            local boon_def = BOON_DEFINITIONS[boon_id]
            if not silent and self.inst.components.talker then
                self.inst.components.talker:Say(string.format("✓ 卸下了 %s", boon_def.name))
            end

            print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "卸下被动恩惠:", boon_def.name)
            return true
        end
    end

    if not silent and self.inst.components.talker then
        self.inst.components.talker:Say("未装备此被动恩惠")
    end
    return false
end

function ModPlayerBoons:CmdShowBoons()
    local msg = string.format("灵韵:%d 已解锁:%d 已装备:%d/%d",
        self.favor,
        self:GetUnlockedCount(),
        #self.equipped_boons,
        self.max_equipped)
    if self.inst.components.talker then
        self.inst.components.talker:Say(msg)
    end
end

-- 获取已解锁的被动恩惠数量
function ModPlayerBoons:GetUnlockedCount()
    local count = 0
    for _ in pairs(self.unlocked_boons) do
        count = count + 1
    end
    return count
end

-- 洗点功能 - 重置所有被动恩惠（不返还点数）
function ModPlayerBoons:RespecAllBoons(cost)
    -- 如果没有装备任何被动恩惠，不需要洗点
    if #self.equipped_boons == 0 then
        if self.inst.components.talker then
            self.inst.components.talker:Say("没有装备任何被动恩惠，无需洗点")
        end
        return false
    end

    -- 洗点不需要费用，直接卸下所有装备的被动恩惠
    local equipped_copy = {}
    for _, boon_id in ipairs(self.equipped_boons) do
        table.insert(equipped_copy, boon_id)
    end

    for _, boon_id in ipairs(equipped_copy) do
        self:UnequipBoon(boon_id, true)  -- 静默卸下，避免重复消息
    end

    print("[商旅巡游录] 玩家", self.inst.userid or "unknown", "洗点完成，卸下了", #equipped_copy, "个被动恩惠")
    return true
end

-- === 法师系支持函数 ===

-- 应用毒伤效果
function ModPlayerBoons:ApplyPoisonDamage(target, damage_per_tick, duration)
    if not target or not target:IsValid() or not target.components.health then return end

    -- 检查目标是否已经中毒
    if target.poison_task then
        target.poison_task:Cancel()
    end

    local ticks = 0
    local max_ticks = duration

    target.poison_task = target:DoPeriodicTask(1, function()
        ticks = ticks + 1
        if target and target:IsValid() and target.components.health and not target.components.health:IsDead() then
            target.components.health:DoDelta(-damage_per_tick, false, "poison")

            -- 毒伤特效
            if target.SoundEmitter then
                target.SoundEmitter:PlaySound("dontstarve/creatures/spider/hit")
            end
        end

        if ticks >= max_ticks then
            target.poison_task:Cancel()
            target.poison_task = nil
        end
    end)

    if self.inst.components.talker then
        self.inst.components.talker:Say("中毒了!")
    end
end

-- 执行传送
function ModPlayerBoons:PerformTeleport(distance)
    local player = self.inst
    if not player or not player:IsValid() then return end

    local x, y, z = player.Transform:GetWorldPosition()
    local angle = player.Transform:GetRotation() * GLOBAL.DEGREES

    -- 计算传送目标位置
    local target_x = x + math.cos(angle) * distance
    local target_z = z - math.sin(angle) * distance

    -- 检查目标位置是否安全
    local ground = GLOBAL.TheWorld.Map:GetTileAtPoint(target_x, 0, target_z)
    if ground == GLOBAL.GROUND.IMPASSABLE or ground == GLOBAL.GROUND.INVALID then
        if player.components.talker then
            player.components.talker:Say("无法传送到那里!")
        end
        return
    end

    -- 执行传送
    if player.Physics then
        player.Physics:Teleport(target_x, 0, target_z)
    else
        player.Transform:SetPosition(target_x, 0, target_z)
    end

    -- 传送特效
    if player.SoundEmitter then
        player.SoundEmitter:PlaySound("dontstarve/common/teleportworm/teleport")
    end

    if player.components.talker then
        player.components.talker:Say("闪现!")
    end

    print("[商旅巡游录] 玩家传送:", distance, "格")
end

-- === 召唤师系支持函数 ===

-- 召唤生物
function ModPlayerBoons:SummonCreature(boon_id, summon_data)
    local player = self.inst
    if not player or not player:IsValid() then return end

    -- 初始化召唤列表
    if not self.summoned_creatures[boon_id] then
        self.summoned_creatures[boon_id] = {}
    end

    -- 清理已死亡的召唤物
    for i = #self.summoned_creatures[boon_id], 1, -1 do
        local creature = self.summoned_creatures[boon_id][i]
        if not creature or not creature:IsValid() or (creature.components.health and creature.components.health:IsDead()) then
            table.remove(self.summoned_creatures[boon_id], i)
        end
    end

    -- 检查数量限制
    if #self.summoned_creatures[boon_id] >= summon_data.max_count then
        if player.components.talker then
            player.components.talker:Say("召唤数量已达上限!")
        end
        return
    end

    -- 生成召唤物
    local x, y, z = player.Transform:GetWorldPosition()
    local offset_angle = math.random() * 2 * math.pi
    local offset_distance = 2
    local spawn_x = x + math.cos(offset_angle) * offset_distance
    local spawn_z = z + math.sin(offset_angle) * offset_distance

    local creature = GLOBAL.SpawnPrefab(summon_data.prefab)
    if not creature then
        print("[商旅巡游录] 召唤失败，无法生成:", summon_data.prefab)
        return
    end

    creature.Transform:SetPosition(spawn_x, 0, spawn_z)

    -- 设置为友好
    if creature.components.combat then
        creature.components.combat:SetTarget(nil)
        creature:AddTag("companion")
        creature:AddTag("summoned")
        creature:RemoveTag("monster")
    end

    -- 设置跟随
    if creature.components.follower then
        creature.components.follower:SetLeader(player)
    end

    -- 设置缩放（如果有）
    if summon_data.scale then
        creature.Transform:SetScale(summon_data.scale, summon_data.scale, summon_data.scale)
        if creature.components.health then
            creature.components.health:SetMaxHealth(creature.components.health.maxhealth * summon_data.scale)
        end
        if creature.components.combat then
            creature.components.combat:SetDefaultDamage(creature.components.combat.defaultdamage * summon_data.scale)
        end
    end

    -- 设置生存时间
    creature:DoTaskInTime(summon_data.duration, function()
        if creature and creature:IsValid() then
            creature:Remove()
        end
    end)

    -- 添加到召唤列表
    table.insert(self.summoned_creatures[boon_id], creature)

    if player.components.talker then
        local boon_def = BOON_DEFINITIONS[boon_id]
        player.components.talker:Say(string.format("召唤了%s!", boon_def.name))
    end

    print("[商旅巡游录] 召唤生物:", summon_data.prefab)
end

-- 解散所有召唤物
function ModPlayerBoons:DismissAllSummons(boon_id)
    if not self.summoned_creatures[boon_id] then return end

    for _, creature in ipairs(self.summoned_creatures[boon_id]) do
        if creature and creature:IsValid() then
            creature:Remove()
        end
    end

    self.summoned_creatures[boon_id] = {}
    print("[商旅巡游录] 解散所有召唤物:", boon_id)
end

-- 应用被动恩惠效果
function ModPlayerBoons:ApplyBoonEffect(boon_id)
    local boon_def = BOON_DEFINITIONS[boon_id]
    if not boon_def then return end

    local player = self.inst
    local cleanup_fn = nil

    -- === 战士系效果 ===
    if boon_def.effect_type == "speed" then
        -- 移速加成
        if player.components.locomotor then
            player.components.locomotor:SetExternalSpeedMultiplier(player, "boon_" .. boon_id, boon_def.effect_value)
            cleanup_fn = function()
                if player and player:IsValid() and player.components.locomotor then
                    player.components.locomotor:RemoveExternalSpeedMultiplier(player, "boon_" .. boon_id)
                end
            end
        end

    elseif boon_def.effect_type == "damage_boost" then
        -- 攻击力提升
        if player.components.combat then
            -- 使用externaldamagemultipliers系统
            -- 正确的参数顺序：SetModifier(source, value, key)
            player.components.combat.externaldamagemultipliers:SetModifier(player, boon_def.effect_value, "boon_" .. boon_id)
            cleanup_fn = function()
                if player and player:IsValid() and player.components.combat and player.components.combat.externaldamagemultipliers then
                    player.components.combat.externaldamagemultipliers:RemoveModifier(player, "boon_" .. boon_id)
                end
            end
        end

    elseif boon_def.effect_type == "combo_system" then
        -- 连击系统
        local function OnAttackOther(inst, data)
            if data and data.target and data.target.GUID then
                local target_guid = data.target.GUID

                -- 重置其他目标的连击层数
                for guid, _ in pairs(self.combo_stacks) do
                    if guid ~= target_guid then
                        self.combo_stacks[guid] = nil
                        if self.combo_timers[guid] then
                            self.combo_timers[guid]:Cancel()
                            self.combo_timers[guid] = nil
                        end
                    end
                end

                -- 增加当前目标的连击层数
                self.combo_stacks[target_guid] = (self.combo_stacks[target_guid] or 0) + 1
                if self.combo_stacks[target_guid] > boon_def.effect_value then
                    self.combo_stacks[target_guid] = boon_def.effect_value
                end

                -- 重置连击计时器
                if self.combo_timers[target_guid] then
                    self.combo_timers[target_guid]:Cancel()
                end
                self.combo_timers[target_guid] = inst:DoTaskInTime(3, function()
                    self.combo_stacks[target_guid] = nil
                    self.combo_timers[target_guid] = nil
                end)

                -- 应用连击伤害加成
                local combo_mult = 1 + (self.combo_stacks[target_guid] - 1) * 0.2 -- 每层+20%伤害
                if data.damage then
                    data.damage = data.damage * combo_mult
                end

                if inst.components.talker and self.combo_stacks[target_guid] > 1 then
                    inst.components.talker:Say(string.format("连击 x%d!", self.combo_stacks[target_guid]))
                end
            end
        end

        player:ListenForEvent("onattackother", OnAttackOther)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("onattackother", OnAttackOther)
                -- 清理连击数据
                for _, timer in pairs(self.combo_timers) do
                    if timer then timer:Cancel() end
                end
                self.combo_stacks = {}
                self.combo_timers = {}
            end
        end

    -- === 法师系效果 ===
    elseif boon_def.effect_type == "poison_damage" then
        -- 毒伤效果
        local function OnAttackOther(inst, data)
            if data and data.target and data.target.components and data.target.components.health then
                self:ApplyPoisonDamage(data.target, boon_def.effect_value.damage, boon_def.effect_value.duration)
            end
        end

        player:ListenForEvent("onattackother", OnAttackOther)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("onattackother", OnAttackOther)
            end
        end

    elseif boon_def.effect_type == "charge_attack" then
        -- 蓄力攻击
        local function OnStartAttack(inst)
            self.charge_start_time = GLOBAL.GetTime()
            self.is_charging = true
        end

        local function OnAttackOther(inst, data)
            if self.is_charging and data and data.damage then
                local charge_time = GLOBAL.GetTime() - self.charge_start_time
                local charge_mult = math.min(1 + charge_time * 0.5, boon_def.effect_value) -- 每秒+50%伤害，最多300%
                data.damage = data.damage * charge_mult

                if charge_mult > 1.5 and inst.components.talker then
                    inst.components.talker:Say(string.format("蓄力打击! x%.1f", charge_mult))
                end
            end
            self.is_charging = false
        end

        -- 使用正确的事件名称
        player:ListenForEvent("attackstart", OnStartAttack)
        player:ListenForEvent("onattackother", OnAttackOther)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("attackstart", OnStartAttack)
                player:RemoveEventCallback("onattackother", OnAttackOther)
            end
        end

    elseif boon_def.effect_type == "teleport_skill" then
        -- 闪现技能 - 简化为按键触发
        -- 注意：这里我们简化实现，实际的双击检测需要更复杂的输入处理
        -- 暂时使用一个标记，让玩家可以通过聊天命令触发传送
        self.has_teleport = true

        cleanup_fn = function()
            self.has_teleport = false
        end

    -- === 召唤师系效果 ===
    elseif boon_def.effect_type == "summon_creature" then
        -- 召唤生物
        self:SummonCreature(boon_id, boon_def.effect_value)
        cleanup_fn = function()
            self:DismissAllSummons(boon_id)
        end

    -- === 农民系效果 ===
    elseif boon_def.effect_type == "plant_growth" then
        -- 植物生长加速
        local growth_task = player:DoPeriodicTask(boon_def.effect_value.check_interval, function()
            self:AcceleratePlantGrowth(boon_def.effect_value.range, boon_def.effect_value.growth_multiplier)
        end)
        cleanup_fn = function()
            if growth_task then
                growth_task:Cancel()
            end
        end

    elseif boon_def.effect_type == "auto_harvest" then
        -- 自动收获
        local harvest_task = player:DoPeriodicTask(boon_def.effect_value.check_interval, function()
            self:AutoHarvestCrops(boon_def.effect_value.range)
        end)
        cleanup_fn = function()
            if harvest_task then
                harvest_task:Cancel()
            end
        end

    elseif boon_def.effect_type == "harvest_blessing" then
        -- 丰收祝福
        local function OnPickSomething(inst, data)
            if data and data.object then
                -- 恢复三维属性
                if player.components.hunger then
                    player.components.hunger:DoDelta(boon_def.effect_value.hunger)
                end
                if player.components.sanity then
                    player.components.sanity:DoDelta(boon_def.effect_value.sanity)
                end
                if player.components.health then
                    player.components.health:DoDelta(boon_def.effect_value.health)
                end

                if player.components.talker then
                    player.components.talker:Say("丰收祝福!")
                end
            end
        end

        player:ListenForEvent("picksomething", OnPickSomething)
        cleanup_fn = function()
            if player and player:IsValid() then
                player:RemoveEventCallback("picksomething", OnPickSomething)
            end
        end
    end

    -- 保存清理函数
    if cleanup_fn then
        self.active_effects[boon_id] = cleanup_fn
    end

    print("[商旅巡游录] 应用被动恩惠效果:", boon_def.name)
end

-- 移除被动恩惠效果
function ModPlayerBoons:RemoveBoonEffect(boon_id)
    local cleanup_fn = self.active_effects[boon_id]
    if cleanup_fn then
        cleanup_fn()
        self.active_effects[boon_id] = nil

        local boon_def = BOON_DEFINITIONS[boon_id]
        print("[商旅巡游录] 移除被动恩惠效果:", boon_def and boon_def.name or boon_id)
    end
end

-- 重新应用所有装备的被动恩惠效果（用于加载存档后）
function ModPlayerBoons:ReapplyAllEffects()
    -- 先清理所有效果
    for boon_id, cleanup_fn in pairs(self.active_effects) do
        cleanup_fn()
    end
    self.active_effects = {}

    -- 重新应用装备的效果
    for _, boon_id in ipairs(self.equipped_boons) do
        self:ApplyBoonEffect(boon_id)
    end

    print("[商旅巡游录] 重新应用", #self.equipped_boons, "个被动恩惠效果")
end

function ModPlayerBoons:OnSave()
    return {
        favor = self.favor,
        unlocked_boons = self.unlocked_boons,
        equipped_boons = self.equipped_boons,
        max_equipped = self.max_equipped,
        -- 不保存运行时数据，重新加载时会重新初始化
    }
end

function ModPlayerBoons:OnLoad(data)
    if data then
        self.favor = data.favor or 0
        self.unlocked_boons = data.unlocked_boons or data.unlocked or {}  -- 兼容旧版本
        self.equipped_boons = data.equipped_boons or data.equipped or {}  -- 兼容旧版本
        self.max_equipped = data.max_equipped or 2

        -- 重新初始化运行时数据
        self.combo_stacks = {}
        self.combo_timers = {}
        self.charge_start_time = 0
        self.is_charging = false
        self.teleport_cooldown = 0
        self.last_move_time = 0
        self.move_key_count = 0
        self.summoned_creatures = {}

        -- 延迟重新应用效果，确保玩家完全加载
        self.inst:DoTaskInTime(1, function()
            self:ReapplyAllEffects()
        end)
    end
end

-- 植物生长加速
function ModPlayerBoons:AcceleratePlantGrowth(range, multiplier)
    local player = self.inst
    if not player or not player:IsValid() then return end

    local x, y, z = player.Transform:GetWorldPosition()
    local ents = GLOBAL.TheSim:FindEntities(x, y, z, range, {"plant"})

    for _, ent in ipairs(ents) do
        if ent and ent:IsValid() and ent.components.growable then
            -- 检查是否正在生长
            if ent.components.growable:IsGrowing() and ent.components.growable.targettime then
                local current_time = GLOBAL.GetTime()
                local remaining = ent.components.growable.targettime - current_time

                if remaining > 0 then
                    -- 计算新的目标时间（加速生长）
                    local new_remaining = remaining / multiplier
                    local new_target_time = current_time + new_remaining

                    -- 取消当前任务并创建新的加速任务
                    if ent.components.growable.task then
                        ent.components.growable.task:Cancel()
                    end

                    ent.components.growable.targettime = new_target_time
                    ent.components.growable.task = ent:DoTaskInTime(new_remaining, function()
                        if ent and ent:IsValid() and ent.components.growable then
                            ent.components.growable:DoGrowth()
                        end
                    end)
                end
            end
        end
    end
end

-- 自动收获作物
function ModPlayerBoons:AutoHarvestCrops(range)
    local player = self.inst
    if not player or not player:IsValid() then return end

    local x, y, z = player.Transform:GetWorldPosition()
    local ents = GLOBAL.TheSim:FindEntities(x, y, z, range, {"pickable"})

    for _, ent in ipairs(ents) do
        if ent and ent:IsValid() and ent.components.pickable then
            -- 检查是否可以采集
            if ent.components.pickable:CanBePicked() then
                -- 自动采集
                local loot = ent.components.pickable:Pick(player)
                if loot then
                    -- 尝试将物品添加到玩家背包
                    if player.components.inventory then
                        local success = player.components.inventory:GiveItem(loot)
                        if not success then
                            -- 如果背包满了，掉落在地上
                            loot.Transform:SetPosition(x, 0, z)
                        end
                    else
                        -- 如果没有背包组件，掉落在地上
                        loot.Transform:SetPosition(x, 0, z)
                    end

                    -- 触发采集事件，让丰收祝福生效
                    player:PushEvent("picksomething", {object = ent, loot = loot})
                end
            end
        end
    end
end

return ModPlayerBoons
