---@meta

---@class replica_hunger
local replica_hunger = {}

---
---author: 
function replica_hunger:GetPercent()
end

---
---author: 
function replica_hunger:OnRemoveFromEntity()
end

---
---author: 
function replica_hunger:DetachClassified()
end

---
---@param classified idk # 
---author: 
function replica_hunger:AttachClassified(classified)
end

---
---@param current idk # 
---author: 
function replica_hunger:SetCurrent(current)
end

---
---author: 
function replica_hunger:IsStarving()
end

---
---author: 
function replica_hunger:GetCurrent()
end

---
---author: 
function replica_hunger:Max()
end

---
---@param max idk # 
---author: 
function replica_hunger:SetMax(max)
end

