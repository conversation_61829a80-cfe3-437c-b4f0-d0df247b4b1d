---@meta

---@class component_skinner
local skinner = {}

---
---author: 
function skinner:SetupNonPlayerData()
end

---
---author: 
function skinner:ClearAllClothing()
end

---
---author: 
function skinner:ClearMonkeyCurse()
end

---
---author: 
function skinner:OnSave()
end

---
---@param data idk # 
---author: 
function skinner:OnLoad(data)
end

---
---@param player idk # 
---author: 
function skinner:CopySkinsFromPlayer(player)
end

---
---author: 
function skinner:GetSkinMode()
end

---
---@param type idk # 
---author: 
function skinner:ClearClothing(type)
end

---
---@param monkey_curse idk # 
---author: 
function skinner:SetMonkeyCurse(monkey_curse)
end

---
---author: 
function skinner:GetClothing()
end

---
---@param skintype idk # 
---@param default_build idk # 
---author: 
function skinner:SetSkinMode(skintype,default_build)
end

---
---author: 
function skinner:GetMonkeyCurse()
end

---
---@param name idk # 
---author: 
function skinner:SetClothing(name)
end

---
---@param skin_name idk # 
---@param skip_beard_setup idk # 
---@param skip_skins_set idk # 
---author: 
function skinner:SetSkinName(skin_name,skip_beard_setup,skip_skins_set)
end

---
---@param anim_state idk # 
---author: 
function skinner:HideAllClothing(anim_state)
end

