---@meta

---@class widget_batover: widget_uianim
---@overload fun(owner:idk): widget_batover
---@field _ctor function #
---@field owner idk #
---@field scrnw idk #
---@field scrnh idk #
---@field soundlevel idk #
---@field sounddelay idk #
local batover = {}

---
---
---author: 
function batover:TriggerBats() end

---
---
---author: 
function batover:UpdateScale() end

---
---@param dt idk #
---
---author: 
function batover:OnUpdate(dt) end

