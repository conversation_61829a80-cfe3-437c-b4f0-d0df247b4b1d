---@meta

---@class component_roseinspectableuser
local roseinspectableuser = {}

---
---@param cooldowntime idk # 
---author: 
function roseinspectableuser:SetCooldownTime(cooldowntime)
end

---
---author: 
function roseinspectableuser:SpawnResidue()
end

---
---author: 
function roseinspectableuser:OnCooldown()
end

---
---author: 
function roseinspectableuser:OnRemoveFromEntity()
end

---
---author: 
function roseinspectableuser:GoOnCooldown()
end

---
---@param duration idk # 
---author: 
function roseinspectableuser:ApplyCooldown(duration)
end

---
---@param pt idk # 
---author: 
function roseinspectableuser:TryToDoRoseInspectionOnPoint(pt)
end

---
---author: 
function roseinspectableuser:GetDebugString()
end

---
---@param data idk # 
---author: 
function roseinspectableuser:OnLoad(data)
end

---
---@param residue idk # 
---author: 
function roseinspectableuser:OnCharlieResidueActivated(residue)
end

---
---author: 
function roseinspectableuser:OnSave()
end

---
---@param target idk # 
---author: 
function roseinspectableuser:TryToDoRoseInspectionOnTarget(target)
end

---
---@param dt idk # 
---author: 
function roseinspectableuser:LongUpdate(dt)
end

---
---@param point idk # 
---author: 
function roseinspectableuser:SetRoseInpectionOnPoint(point)
end

---
---author: 
function roseinspectableuser:IsInCooldown()
end

---
---author: 
function roseinspectableuser:DoRoseInspectionOnPoint()
end

---
---@param reason idk # 
---@param failed idk # 
---author: 
function roseinspectableuser:DoQuip(reason,failed)
end

---
---author: 
function roseinspectableuser:ForceDecayResidue()
end

---
---@param target idk # 
---author: 
function roseinspectableuser:SetRoseInpectionOnTarget(target)
end

