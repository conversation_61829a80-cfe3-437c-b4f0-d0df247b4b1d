---@meta

---@class component_plantregrowth
local plantregrowth = {}

---
---@param rate idk # 
---author: 
function plantregrowth:SetRegrowthRate(rate)
end

---
---@param data idk # 
---author: 
function plantregrowth:OnLoad(data)
end

---
---@param product idk # 
---author: 
function plantregrowth:SetProduct(product)
end

---
---author: 
function plantregrowth:GetDebugString()
end

---
---author: 
function plantregrowth:OnRemoveEntity()
end

---
---author: 
function plantregrowth:TrySpawnNearby()
end

---
---author: 
function plantregrowth:OnSave()
end

---
---author: 
function plantregrowth:ResetGrowthTime()
end

---
---author: 
function plantregrowth:OnRemoveFromEntity()
end

---
---@param tag idk # 
---author: 
function plantregrowth:SetSearchTag(tag)
end

