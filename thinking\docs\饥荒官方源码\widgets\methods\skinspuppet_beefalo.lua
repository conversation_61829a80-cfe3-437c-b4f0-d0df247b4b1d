---@meta

---@class widget_skinspuppet_beefalo: widget_button
---@overload fun(): widget_skinspuppet_beefalo
---@field _ctor function #
---@field anim idk #
---@field animstate idk #
---@field currentanimbank idk #
---@field current_idle_anim idk #
---@field default_build idk #
---@field last_skins idk #
---@field shadow idk #
---@field prefabname idk #
local skinspuppet_beefalo = {}

---
---
---author: 
function skinspuppet_beefalo:AddShadow() end

---
---@param prefabname idk #
---@param base_item idk #
---@param clothing_names idk #
---@param skip_change_emote idk #
---@param inst idk #
---
---author: 
function skinspuppet_beefalo:SetSkins(prefabname, base_item, clothing_names, skip_change_emote, inst) end

