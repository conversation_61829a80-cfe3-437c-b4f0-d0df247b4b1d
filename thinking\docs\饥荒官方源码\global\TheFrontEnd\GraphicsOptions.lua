---@meta

---@class GraphicsOptions
local gOpts = {}

---
---UNKNOWN
---
---author:
function gOpts:IsFullScreenEnabled() end

---
---UNKNOWN
---
---author:
function gOpts:GetNumDisplayModes() end

---
---UNKNOWN
---
---author:
function gOpts:GetNumRefreshRates() end

---
---UNKNOWN
---
---author:
function gOpts:GetFullscreenDisplayRefreshRate() end

---
---UNKNOWN
---
---author:
function gOpts:GetDisplayName() end

---
---UNKNOWN
---
---author:
function gOpts:DisableLightMapComponent() end

---
---UNKNOWN
---
---author:
function gOpts:ToggleFullScreen() end

---
---UNKNOWN
---
---author:
function gOpts:SetDisplayMode() end

---
---UNKNOWN
---
---author:
function gOpts:GetRefreshRate() end

---
---UNKNOWN
---
---author:
function gOpts:SetFullScreen() end

---
---UNKNOWN
---
---author:
function gOpts:GetDisplayMode() end

---
---UNKNOWN
---
---author:
function gOpts:GetNumDisplays() end

---
---UNKNOWN
---
---author:
function gOpts:GetCurrentDisplayModeID() end

---
---UNKNOWN
---
---author:
function gOpts:IsFullScreen() end

---
---UNKNOWN
---
---author:
function gOpts:EnableStencil() end

---
---UNKNOWN
---
---author:
function gOpts:SetSmallTexturesMode() end

---
---UNKNOWN
---
---author:
function gOpts:DisableStencil() end

---
---UNKNOWN
---
---author:
function gOpts:EnableLightMapComponent() end

---
---UNKNOWN
---
---author:
function gOpts:IsSmallTexturesMode() end

---
---UNKNOWN
---
---author:
function gOpts:GetSmallTexturesModeSettingValue() end

---
---UNKNOWN
---
---author:
function gOpts:GetFullscreenDisplayID() end

