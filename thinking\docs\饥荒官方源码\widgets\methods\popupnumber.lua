---@meta

---@class widget_popupnumber: widget_widget
---@overload fun(owner:idk, val:idk, size:idk, pos:idk, height:idk, colour:idk, burst:idk): widget_popupnumber
---@field _ctor function #
---@field owner idk #
---@field text idk #
---@field pos idk #
---@field colour idk #
---@field xoffs idk #
---@field yoffs idk #
---@field xoffs2 idk #
---@field yoffs2 idk #
---@field dir idk #
---@field rise idk #
---@field drop idk #
---@field speed idk #
---@field progress idk #
---@field burst idk #
local popupnumber = {}

---
---@param dt idk #
---
---author: 
function popupnumber:OnUpdate(dt) end

