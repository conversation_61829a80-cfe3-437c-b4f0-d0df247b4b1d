---@meta

---@class component_temperatureoverrider
local temperatureoverrider = {}

---
---author: 
function temperatureoverrider:OnRemoveEntity()
end

---
---@param radius idk # 
---author: 
function temperatureoverrider:SetRadius(radius)
end

---
---@param new idk # 
---@param old idk # 
---author: 
function temperatureoverrider:SetActiveRadius_Internal(new,old)
end

---
---author: 
function temperatureoverrider:Disable()
end

---
---@param temperature idk # 
---author: 
function temperatureoverrider:SetTemperature(temperature)
end

---
---author: 
function temperatureoverrider:GetActiveRadius()
end

---
---author: 
function temperatureoverrider:Enable()
end

---
---author: 
function temperatureoverrider:OnRemoveFromEntity()
end

---
---author: 
function temperatureoverrider:GetTemperature()
end

