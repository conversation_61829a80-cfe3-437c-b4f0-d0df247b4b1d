---@meta

---@class component_hitcher
local hitcher = {}

---
---author: 
function hitcher:GetHitched()
end

---
---@param setting idk # 
---author: 
function hitcher:Lock(setting)
end

---
---@param target idk # 
---author: 
function hitcher:SetHitched(target)
end

---
---author: 
function hitcher:Unhitch()
end

---
---@param data idk # 
---author: 
function hitcher:OnLoad(data)
end

---
---author: 
function hitcher:OnSave()
end

