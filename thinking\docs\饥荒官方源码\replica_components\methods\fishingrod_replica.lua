---@meta

---@class replica_fishingrod
local replica_fishingrod = {}

---
---author: 
function replica_fishingrod:HasCaughtFish()
end

---
---@param caughtfish idk # 
---author: 
function replica_fishingrod:SetCaughtFish(caughtfish)
end

---
---@param target idk # 
---author: 
function replica_fishingrod:SetTarget(target)
end

---
---author: 
function replica_fishingrod:HasHookedFish()
end

---
---author: 
function replica_fishingrod:GetTarget()
end

---
---@param hookedfish idk # 
---author: 
function replica_fishingrod:SetHookedFish(hookedfish)
end

