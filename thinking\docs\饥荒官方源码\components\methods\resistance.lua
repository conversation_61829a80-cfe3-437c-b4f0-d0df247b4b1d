---@meta

---@class component_resistance
local resistance = {}

---
---@param attacker idk # 
---@param weapon idk # 
---author: 
function resistance:<PERSON><PERSON><PERSON><PERSON><PERSON>(attacker,weapon)
end

---
---author: 
function resistance:ShouldResistDamage()
end

---
---@param tag idk # 
---author: 
function resistance:Has<PERSON><PERSON><PERSON>ceToTag(tag)
end

---
---author: 
function resistance:GetDebugString()
end

---
---@param tag idk # 
---author: 
function resistance:AddResistance(tag)
end

---
---@param fn idk # 
---author: 
function resistance:SetOnResistDamageFn(fn)
end

---
---@param damage_amount idk # 
---author: 
function resistance:ResistDamage(damage_amount)
end

---
---@param fn idk # 
---author: 
function resistance:SetShouldResistFn(fn)
end

---
---@param tag idk # 
---author: 
function resistance:RemoveResistance(tag)
end

