---@meta

---@class component_raindome
local raindome = {}

---
---author: 
function raindome:OnRemoveEntity()
end

---
---@param radius idk # 
---author: 
function raindome:SetRadius(radius)
end

---
---@param dt idk # 
---author: 
function raindome:OnUpdate(dt)
end

---
---author: 
function raindome:Enable()
end

---
---author: 
function raindome:GetActiveRadius()
end

---
---author: 
function raindome:Disable()
end

---
---author: 
function raindome:OnRemoveFromEntity()
end

---
---@param new idk # 
---@param old idk # 
---author: 
function raindome:SetActiveRadius_Internal(new,old)
end

