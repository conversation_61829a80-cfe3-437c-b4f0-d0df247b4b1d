---@meta

---@class component_skinner_beefalo
local skinner_beefalo = {}

---
---@param type idk # 
---author: 
function skinner_beefalo:ClearClothing(type)
end

---
---author: 
function skinner_beefalo:ClearAllClothing()
end

---
---@param clothing idk # 
---author: 
function skinner_beefalo:reloadclothing(clothing)
end

---
---author: 
function skinner_beefalo:GetClothing()
end

---
---author: 
function skinner_beefalo:OnSave()
end

---
---@param skins idk # 
---@param player idk # 
---author: 
function skinner_beefalo:ApplyTargetSkins(skins,player)
end

---
---@param anim_state idk # 
---author: 
function skinner_beefalo:HideAllClothing(anim_state)
end

---
---@param name idk # 
---author: 
function skinner_beefalo:SetClothing(name)
end

