---@meta

---@class component_updatelooper
local updatelooper = {}

---
---author: 
function updatelooper:OnRemoveEntity()
end

---
---@param dt idk # 
---author: 
function updatelooper:OnWallUpdate(dt)
end

---
---@param fn idk # 
---author: 
function updatelooper:RemoveOnWallUpdateFn(fn)
end

---
---@param fn idk # 
---author: 
function updatelooper:AddOnWallUpdateFn(fn)
end

---
---@param fn idk # 
---author: 
function updatelooper:RemovePostUpdateFn(fn)
end

---
---@param fn idk # 
---author: 
function updatelooper:AddOnUpdateFn(fn)
end

---
---@param dt idk # 
---author: 
function updatelooper:LongUpdate(dt)
end

---
---@param fn idk # 
---author: 
function updatelooper:AddPostUpdateFn(fn)
end

---
---@param fn idk # 
---author: 
function updatelooper:RemoveLongUpdateFn(fn)
end

---
---@param dt idk # 
---author: 
function updatelooper:OnUpdate(dt)
end

---
---@param fn idk # 
---author: 
function updatelooper:RemoveOnUpdateFn(fn)
end

---
---author: 
function updatelooper:OnRemoveFromEntity()
end

---
---@param fn idk # 
---author: 
function updatelooper:AddLongUpdateFn(fn)
end

