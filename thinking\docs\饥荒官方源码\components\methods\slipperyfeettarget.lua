---@meta

---@class component_slipperyfeettarget
local slipperyfeettarget = {}

---
---@param fn idk # 
---author: 
function slipperyfeettarget:SetIsSlipperyAtPoint(fn)
end

---
---@param target idk # 
---author: 
function slipperyfeettarget:GetSlipperyRate(target)
end

---
---@param fn idk # 
---author: 
function slipperyfeettarget:SetSlipperyRate(fn)
end

---
---author: 
function slipperyfeettarget:OnRemoveFromEntity()
end

---
---@param x idk # 
---@param y idk # 
---@param z idk # 
---author: 
function slipperyfeettarget:IsSlipperyAtPosition(x,y,z)
end

