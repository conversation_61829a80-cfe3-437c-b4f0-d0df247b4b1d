---@meta

---@class component_sheltered
local sheltered = {}

---
---@param issheltered idk # 
---@param level idk # 
---author: 
function sheltered:SetSheltered(issheltered,level)
end

---
---author: 
function sheltered:Stop()
end

---
---@param dt idk # 
---author: 
function sheltered:OnUpdate(dt)
end

---
---author: 
function sheltered:Start()
end

---
---author: 
function sheltered:OnRemoveFromEntity()
end

---
---author: 
function sheltered:GetDebugString()
end

