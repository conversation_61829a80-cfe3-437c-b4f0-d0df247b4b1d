---@meta

---@class component_teamattacker
local teamattacker = {}

---
---author: 
function teamattacker:SearchForTeam()
end

---
---author: 
function teamattacker:JoinFormation()
end

---
---author: 
function teamattacker:GetDebugString()
end

---
---author: 
function teamattacker:LeaveTeam()
end

---
---@param dt idk # 
---author: 
function teamattacker:OnUpdate(dt)
end

---
---author: 
function teamattacker:GetOrders()
end

---
---author: 
function teamattacker:OnEntityWake()
end

---
---author: 
function teamattacker:ShouldGoHome()
end

---
---author: 
function teamattacker:LeaveFormation()
end

---
---author: 
function teamattacker:OnEntitySleep()
end

