---@meta

---@class component_lunarthrall_plantspawner
local lunarthrall_plantspawner = {}

---
---@param thrall idk # 
---author: 
function lunarthrall_plantspawner:MoveGestaltToPlant(thrall)
end

---
---@param newents idk # 
---@param data idk # 
---author: 
function lunarthrall_plantspawner:LoadPostPass(newents,data)
end

---
---author: 
function lunarthrall_plantspawner:FindHerd()
end

---
---author: 
function lunarthrall_plantspawner:FindPlant()
end

---
---@param target idk # 
---@param rift idk # 
---author: 
function lunarthrall_plantspawner:SpawnGestalt(target,rift)
end

---
---@param plantable idk # 
---author: 
function lunarthrall_plantspawner:setHerdsOnPlantable(plantable)
end

---
---@param dt idk # 
---author: 
function lunarthrall_plantspawner:LongUpdate(dt)
end

---
---author: 
function lunarthrall_plantspawner:RemoveWave()
end

---
---author: 
function lunarthrall_plantspawner:GetDebugString()
end

---
---@param target idk # 
---author: 
function lunarthrall_plantspawner:SpawnPlant(target)
end

---
---@param target idk # 
---author: 
function lunarthrall_plantspawner:InvadeTarget(target)
end

---
---@param data idk # 
---author: 
function lunarthrall_plantspawner:OnLoad(data)
end

---
---author: 
function lunarthrall_plantspawner:OnSave()
end

