---@meta

---@class component_mightygym
local mightygym = {}

---
---@param weight idk # 
---@param slot idk # 
---author: 
function mightygym:SetWeightSymbol(weight,slot)
end

---
---author: 
function mightygym:CheckForWeight()
end

---
---@param level idk # 
---@param target idk # 
---author: 
function mightygym:SetLevelArt(level,target)
end

---
---@param player idk # 
---author: 
function mightygym:CharacterExitGym(player)
end

---
---@param perfect idk # 
---author: 
function mightygym:CalculateMightiness(perfect)
end

---
---@param doer idk # 
---author: 
function mightygym:CanWorkout(doer)
end

---
---author: 
function mightygym:StopWorkout()
end

---
---@param player idk # 
---author: 
function mightygym:CharacterEnterGym(player)
end

---
---@param doer idk # 
---@param skin_mode idk # 
---author: 
function mightygym:SetSkinModeOnGym(doer,skin_mode)
end

---
---author: 
function mightygym:InUse()
end

---
---@param item idk # 
---@param swapitem idk # 
---author: 
function mightygym:SwapWeight(item,swapitem)
end

---
---@param weight idk # 
---@param slot idk # 
---author: 
function mightygym:LoadWeight(weight,slot)
end

---
---@param doer idk # 
---author: 
function mightygym:StartWorkout(doer)
end

---
---author: 
function mightygym:CalcWeight()
end

---
---author: 
function mightygym:UnloadWeight()
end

