---@meta

---@class widget_ingredientui: widget_imagebutton
---@overload fun(atlas:idk, image:idk, quantity:idk, on_hand:idk, has_enough:idk, name:idk, owner:idk, recipe_type:idk, quant_text_scale:idk, ingredient_recipe:idk): widget_ingredientui
---@field _ctor function #
---@field bg idk #
---@field ing idk #
---@field quant idk #
---@field recipe_type idk #
---@field has_enough idk #
---@field owner idk #
---@field fg idk #
---@field ingredient_recipe idk #
---@field onclick idk #
---@field ongainfocus idk #
---@field sub_ingredients idk #
---@field background idk #
---@field ingredients idk #
---@field _scale idk #
---@field onlosefocus idk #
local ingredientui = {}

