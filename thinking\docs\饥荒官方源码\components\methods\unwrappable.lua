---@meta

---@class component_unwrappable
local unwrappable = {}

---
---@param items idk # 
---@param doer idk # 
---author: 
function unwrappable:WrapItems(items,doer)
end

---
---@param doer idk # 
---author: 
function unwrappable:Unwrap(doer)
end

---
---author: 
function unwrappable:OnSave()
end

---
---@param fn idk # 
---author: 
function unwrappable:SetOnUnwrappedFn(fn)
end

---
---@param fn idk # 
---author: 
function unwrappable:SetOnWrappedFn(fn)
end

---
---@param data idk # 
---author: 
function unwrappable:OnLoad(data)
end

