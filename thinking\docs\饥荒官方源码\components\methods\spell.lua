---@meta

---@class component_spell
local spell = {}

---
---author: 
function spell:OnFinish()
end

---
---@param data idk # 
---author: 
function spell:OnLoad(data)
end

---
---author: 
function spell:OnTarget()
end

---
---@param target idk # 
---author: 
function spell:SetTarget(target)
end

---
---@param variables idk # 
---author: 
function spell:SetVariables(variables)
end

---
---author: 
function spell:StartSpell()
end

---
---author: 
function spell:OnSave()
end

---
---@param dt idk # 
---author: 
function spell:OnUpdate(dt)
end

---
---@param newents idk # 
---@param data idk # 
---author: 
function spell:LoadPostPass(newents,data)
end

---
---author: 
function spell:ResumeSpell()
end

---
---author: 
function spell:OnStart()
end

