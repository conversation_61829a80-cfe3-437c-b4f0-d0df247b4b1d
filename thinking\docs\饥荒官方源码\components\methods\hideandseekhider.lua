---@meta

---@class component_hideandseekhider
local hideandseekhider = {}

---
---@param newents idk # 
---@param data idk # 
---author: 
function hideandseekhider:LoadPostPass(newents,data)
end

---
---author: 
function hideandseekhider:Abort()
end

---
---author: 
function hideandseekhider:IsPlaying()
end

---
---@param hiding_spot idk # 
---@param timeout_time idk # 
---@param isloading idk # 
---author: 
function hideandseekhider:GoHide(hiding_spot,timeout_time,isloading)
end

---
---author: 
function hideandseekhider:CanPlayHideAndSeek()
end

---
---author: 
function hideandseekhider:IsHidden()
end

---
---@param doer idk # 
---author: 
function hideandseekhider:Found(doer)
end

---
---author: 
function hideandseekhider:GetDebugString()
end

---
---author: 
function hideandseekhider:OnSave()
end

