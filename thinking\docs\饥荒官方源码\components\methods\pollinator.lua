---@meta

---@class component_pollinator
local pollinator = {}

---
---author: 
function pollinator:GetDebugString()
end

---
---author: 
function pollinator:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>()
end

---
---author: 
function pollinator:C<PERSON>Flower()
end

---
---author: 
function pollinator:CheckFlowerDensity()
end

---
---@param flower idk # 
---author: 
function pollinator:CanPollinate(flower)
end

---
---author: 
function pollinator:OnRemoveFromEntity()
end

---
---@param flower idk # 
---author: 
function pollinator:Pollinate(flower)
end

