---@meta

---@class MiniMapEntity
local MiniMapEntity = {}

---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:GetTextureHandle(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:ResetOffset(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:ShowArea(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:GetZoom(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:AddRenderLayer(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:AddAtlas(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:ToggleVisibility(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:ClearRevealedAreas(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:Offset(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:Zoom(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:EnableFogOfWar(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:SetEffects(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:WorldPosToMapPos(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:IsVisible(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:MapPosToWorldPos(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:RebuildLayer(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:DrawForgottenFogOfWar(...) end
---
---UNKNOWN
---
---@param ... any
---author:
function MiniMapEntity:ContinuouslyClearRevealedAreas(...) end
