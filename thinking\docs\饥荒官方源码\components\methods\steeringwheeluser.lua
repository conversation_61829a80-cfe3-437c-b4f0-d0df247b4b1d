---@meta

---@class component_steeringwheeluser
local steeringwheeluser = {}

---
---@param dir_x idk # 
---@param dir_z idk # 
---author: 
function steeringwheeluser:SteerInDir(dir_x,dir_z)
end

---
---@param steering_wheel idk # 
---author: 
function steeringwheeluser:SetSteeringWheel(steering_wheel)
end

---
---@param dt idk # 
---author: 
function steeringwheeluser:OnUpdate(dt)
end

---
---@param pos_x idk # 
---@param pos_z idk # 
---author: 
function steeringwheeluser:Steer(pos_x,pos_z)
end

---
---author: 
function steeringwheeluser:GetBoat()
end

