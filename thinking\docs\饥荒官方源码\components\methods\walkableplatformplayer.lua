---@meta

---@class component_walkableplatformplayer
local walkableplatformplayer = {}

---
---author: 
function walkableplatformplayer:OnRemoveEntity()
end

---
---author: 
function walkableplatformplayer:StartBoatMusicTest()
end

---
---author: 
function walkableplatformplayer:<PERSON>BoatCameraZooms()
end

---
---author: 
function walkableplatformplayer:StopBoatCamera()
end

---
---author: 
function walkableplatformplayer:StopBoatMusicTest()
end

---
---author: 
function walkableplatformplayer:StopBoatCameraZooms()
end

---
---author: 
function walkableplatformplayer:TestForPlatform()
end

---
---@param platform idk # 
---author: 
function walkableplatformplayer:GetOnPlatform(platform)
end

---
---author: 
function walkableplatformplayer:GetOffPlatform()
end

---
---author: 
function walkableplatformplayer:StartBoatCamera()
end

