---@meta

---@class component_sinkholespawner
local sinkholespawner = {}

---
---@param spawnpt idk # 
---author: 
function sinkholespawner:SpawnSinkhole(spawnpt)
end

---
---@param targetinfo idk # 
---author: 
function sinkholespawner:UpdateTarget(targetinfo)
end

---
---author: 
function sinkholespawner:GetDebugString()
end

---
---@param data idk # 
---author: 
function sinkholespawner:OnLoad(data)
end

---
---author: 
function sinkholespawner:StopSinkholes()
end

---
---@param targetinfo idk # 
---author: 
function sinkholespawner:DoTargetAttack(targetinfo)
end

---
---@param dt idk # 
---author: 
function sinkholespawner:OnUpdate(dt)
end

---
---author: 
function sinkholespawner:OnSave()
end

---
---author: 
function sinkholespawner:PushRemoteTargets()
end

---
---@param targetinfo idk # 
---author: 
function sinkholespawner:DoTargetWarning(targetinfo)
end

