---@meta

---@class component_pocketwatch
local pocketwatch = {}

---
---@param doer idk # 
---@param target idk # 
---@param pos idk # 
---author: 
function pocketwatch:CanCast(doer,target,pos)
end

---
---author: 
function pocketwatch:OnRemoveFromEntity()
end

---
---@param doer idk # 
---@param target idk # 
---@param pos idk # 
---@return boolean
---author: 
function pocketwatch:CastSpell(doer,target,pos)
end

