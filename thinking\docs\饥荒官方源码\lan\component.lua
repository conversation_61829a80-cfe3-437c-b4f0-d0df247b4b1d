---@meta

---@alias componentID string # 组件名
---| "acidbatwavemanager"
---| "acidinfusible"
---| "acidlevel"
---| "activatable"
---| "age"
---| "ambientlighting"
---| "ambientsound"
---| "amorphous"
---| "amphibiouscreature"
---| "anchor"
---| "aoespell"
---| "aoetargeting"
---| "aoeweapon_base"
---| "aoeweapon_leap"
---| "aoeweapon_lunge"
---| "appraisable"
---| "archivemanager"
---| "areaaware"
---| "area_trigger"
---| "area_unlock"
---| "armor"
---| "attackdodger"
---| "attunable"
---| "attuner"
---| "aura"
---| "autosaver"
---| "autoterraformer"
---| "bait"
---| "balloonmaker"
---| "bathbomb"
---| "bathbombable"
---| "batspawner"
---| "battery"
---| "batteryuser"
---| "battleborn"
---| "beard"
---| "beargerspawner"
---| "beaverness"
---| "bedazzlement"
---| "bedazzler"
---| "beefalometrics"
---| "birdattractor"
---| "birdspawner"
---| "blinkstaff"
---| "bloomer"
---| "bloomness"
---| "blowinwind"
---| "boatai"
---| "boatcannon"
---| "boatcannonuser"
---| "boatcrew"
---| "boatdrag"
---| "boatdrifter"
---| "boatleak"
---| "boatmagnet"
---| "boatmagnetbeacon"
---| "boatpatch"
---| "boatphysics"
---| "boatracecrew"
---| "boatrace_proximitybeacon"
---| "boatrace_proximitychecker"
---| "boatring"
---| "boatringdata"
---| "boatrotator"
---| "boattrail"
---| "boattrailmover"
---| "book"
---| "bosstargeter"
---| "brightmarespawner"
---| "brush"
---| "brushable"
---| "builder"
---| "bundlemaker"
---| "bundler"
---| "burnable"
---| "butterflyspawner"
---| "canopylightrays"
---| "canopyshadows"
---| "carefulwalker"
---| "carnivaldecor"
---| "carnivaldecorranker"
---| "carnivalevent"
---| "carnivalgamefeedable"
---| "carnivalgameitem"
---| "carnivalgameshooter"
---| "carnivalhostsummon"
---| "catcher"
---| "cattoy"
---| "caveins"
---| "caveweather"
---| "channelable"
---| "channelcastable"
---| "channelcaster"
---| "charliecutscene"
---| "chessunlocks"
---| "childspawner"
---| "circler"
---| "circuitnode"
---| "clock"
---| "closeinspector"
---| "coach"
---| "colouradder"
---| "colouraddersync"
---| "colourcube"
---| "colourtweener"
---| "combat"
---| "commander"
---| "complexprojectile"
---| "compostingbin"
---| "constructionbuilder"
---| "constructionbuilderuidata"
---| "constructionplans"
---| "constructionsite"
---| "container"
---| "container_proxy"
---| "cookable"
---| "cookbookupdater"
---| "cooker"
---| "cookiecutterdrill"
---| "cooldown"
---| "crabkingspawner"
---| "craftingstation"
---| "crewmember"
---| "crittertraits"
---| "crop"
---| "cursable"
---| "curseditem"
---| "custombuildmanager"
---| "cyclable"
---| "damagereflect"
---| "damagetracker"
---| "damagetypebonus"
---| "damagetyperesist"
---| "dataanalyzer"
---| "daywalkerspawner"
---| "debuff"
---| "debuffable"
---| "debugger"
---| "decay"
---| "deciduoustreeupdater"
---| "deerclopsspawner"
---| "deerherding"
---| "deerherdspawner"
---| "deployable"
---| "deployhelper"
---| "desolationspawner"
---| "despawnfader"
---| "digester"
---| "disappears"
---| "discoverable"
---| "diseaseable"
---| "distancefade"
---| "distancetracker"
---| "dockmanager"
---| "domesticatable"
---| "drawable"
---| "drawingtool"
---| "drownable"
---| "dryable"
---| "dryer"
---| "dsp"
---| "dumbbelllifter"
---| "dynamicmusic"
---| "eater"
---| "edible"
---| "efficientuser"
---| "electricattacks"
---| "embarker"
---| "emitter"
---| "entitytracker"
---| "epicscare"
---| "equippable"
---| "erasablepaper"
---| "experiencecollector"
---| "expertsailor"
---| "explosive"
---| "explosiveresist"
---| "fader"
---| "fan"
---| "farming_manager"
---| "farmplantable"
---| "farmplantstress"
---| "farmplanttendable"
---| "farmsoildrinker"
---| "farmtiller"
---| "feasts"
---| "fencerotator"
---| "fertilizable"
---| "fertilizer"
---| "fertilizerresearchable"
---| "fillable"
---| "finiteuses"
---| "firebug"
---| "firedetector"
---| "firefx"
---| "fishable"
---| "fishingnet"
---| "fishingnetvisualizer"
---| "fishingrod"
---| "fishschool"
---| "floater"
---| "flotationdevice"
---| "flotsamgenerator"
---| "focalpoint"
---| "follower"
---| "followerherder"
---| "foodaffinity"
---| "foodmemory"
---| "forcecompostable"
---| "forestdaywalkerspawner"
---| "forestpetrification"
---| "forestresourcespawner"
---| "forgerepair"
---| "forgerepairable"
---| "formationfollower"
---| "formationleader"
---| "freezable"
---| "freezefx"
---| "friendlevels"
---| "frograin"
---| "frostybreather"
---| "fuel"
---| "fueled"
---| "fueler"
---| "fuelmaster"
---| "furnituredecor"
---| "furnituredecortaker"
---| "ghostlybond"
---| "ghostlyelixir"
---| "ghostlyelixirable"
---| "giftreceiver"
---| "gingerbreadhunter"
---| "grabbable"
---| "grogginess"
---| "groomer"
---| "grottowarmanager"
---| "grottowaterfallsoundcontroller"
---| "groundcreep"
---| "groundpounder"
---| "groundshadowhandler"
---| "grouptargeter"
---| "growable"
---| "grower"
---| "grue"
---| "guardian"
---| "gym"
---| "halloweenmoonmutable"
---| "halloweenpotionmoon"
---| "hallucinations"
---| "harvestable"
---| "hatchable"
---| "hauntable"
---| "healer"
---| "health"
---| "healthbar"
---| "healthsyncer"
---| "healthtrigger"
---| "heater"
---| "heavyobstaclephysics"
---| "heavyobstacleusetarget"
---| "herd"
---| "herdmember"
---| "hideandseeker"
---| "hideandseekgame"
---| "hideandseekhider"
---| "hideandseekhidingspot"
---| "hideout"
---| "highlight"
---| "highlightchild"
---| "hitchable"
---| "hitcher"
---| "homeseeker"
---| "hounded"
---| "houndedtarget"
---| "hudindicatable"
---| "hudindicatablemanager"
---| "hudindicatorwatcher"
---| "hull"
---| "hullhealth"
---| "hunger"
---| "hunter"
---| "incinerator"
---| "incrementalproducer"
---| "inkable"
---| "inspectable"
---| "inspectaclesparticipant"
---| "instrument"
---| "insulator"
---| "inventory"
---| "inventoryitem"
---| "inventoryitemholder"
---| "inventoryitemmoisture"
---| "itemaffinity"
---| "itemweigher"
---| "key"
---| "kitcoon"
---| "kitcoonden"
---| "klaussackkey"
---| "klaussacklock"
---| "klaussackloot"
---| "klaussackspawner"
---| "knownlocations"
---| "kramped"
---| "lavaarenaeventstate"
---| "lavaarenamobtracker"
---| "lavaarenamusic"
---| "leader"
---| "lighter"
---| "lightningblocker" # 避雷组件,这个优先级比避雷针tag高
---| "lighttweener"
---| "lock"
---| "locomotor"
---| "lootdropper"
---| "lordfruitflytrigger"
---| "lunarhailmanager"
---| "lunarplant_tentacle_weapon"
---| "lunarriftmutationsmanager"
---| "lunarthrall_plantspawner"
---| "lureplantspawner"
---| "machine"
---| "madsciencelab"
---| "magician"
---| "magiciantool"
---| "malbatrossspawner"
---| "map"
---| "maprecorder"
---| "maprevealable"
---| "maprevealer"
---| "mapspotrevealer"
---| "markable"
---| "markable_proxy"
---| "mast"
---| "maxhealer"
---| "maxlightspawner"
---| "maxwelltalker"
---| "mermcandidate"
---| "mermkingmanager"
---| "messagebottlemanager"
---| "meteorshower"
---| "miasmamanager"
---| "miasmawatcher"
---| "mightiness"
---| "mightydumbbell"
---| "mightygym"
---| "migrationpetowner"
---| "mine"
---| "minigame"
---| "minigame_participator"
---| "minigame_spectator"
---| "minionspawner"
---| "moisture"
---| "moistureimmunity"
---| "mood"
---| "moonaltarlink"
---| "moonaltarlinktarget"
---| "moonbeastspawner"
---| "moonrelic"
---| "moonstormlightningmanager"
---| "moonstormmanager"
---| "moonstorms"
---| "moonstormwatcher"
---| "moontrader"
---| "moosespawner"
---| "murderable"
---| "named"
---| "nightlightmanager"
---| "nightmareclock"
---| "nis"
---| "npc_talker"
---| "nutrients_visual_manager"
---| "oar"
---| "oasis"
---| "objectspawner"
---| "occupiable"
---| "occupier"
---| "ocean"
---| "oceancolor"
---| "oceanfishable"
---| "oceanfishinghook"
---| "oceanfishingrod"
---| "oceanfishingtackle"
---| "oceanicemanager"
---| "oceanthrowable"
---| "oceantrawler"
---| "oldager"
---| "papereraser"
---| "parryweapon"
---| "penguinspawner"
---| "periodicspawner"
---| "perishable"
---| "pethealthbar"
---| "petleash"
---| "petrifiable"
---| "pickable"
---| "pinnable"
---| "piratespawner"
---| "placer"
---| "planardamage"
---| "planardefense"
---| "planarentity"
---| "plantable"
---| "plantregistryupdater"
---| "plantregrowth"
---| "plantresearchable"
---| "platformhopdelay"
---| "playbill"
---| "playbill_lecturn"
---| "playeractionpicker"
---| "playeravatardata"
---| "playercontroller"
---| "playerhearing"
---| "playerinspectable"
---| "playerlightningtarget"
---| "playermetrics"
---| "playerprox"
---| "playerspawner"
---| "playertargetindicator"
---| "playervision"
---| "playervoter"
---| "pocketwatch"
---| "pocketwatch_dismantler"
---| "pointofinterest"
---| "pollinator"
---| "poppable"
---| "portablestructure"
---| "positionalwarp"
---| "possessedaxe"
---| "powerload"
---| "preservative"
---| "preserver"
---| "projectedeffects"
---| "projectile"
---| "propagator"
---| "prophider"
---| "prototyper"
---| "quagmire_hangriness"
---| "quagmire_map"
---| "quagmire_music"
---| "quagmire_recipebook"
---| "quagmire_recipeprices"
---| "quaker"
---| "questowner"
---| "raindome"
---| "raindomewatcher"
---| "rainimmunity"
---| "rampingspawner"
---| "reader"
---| "recallmark"
---| "rechargeable"
---| "recipescanner"
---| "recipestockpile"
---| "regrowthmanager"
---| "reloaditem"
---| "remoteteleporter"
---| "repairable"
---| "repairer"
---| "repellent"
---| "replayproxy"
---| "researchpointconverter"
---| "resistance"
---| "reticule"
---| "retrofitcavemap_anr"
---| "retrofitforestmap_anr"
---| "revivablecorpse"
---| "rideable"
---| "rider"
---| "riftspawner"
---| "rooted"
---| "roseinspectable"
---| "roseinspectableuser"
---| "ruinsshadelingspawner"
---| "saddler"
---| "saltlicker"
---| "sandstorms"
---| "sandstormwatcher"
---| "sanity"
---| "sanityaura"
---| "savedrotation"
---| "savedscale"
---| "scaler"
---| "scenariorunner"
---| "schoolspawner"
---| "scrapbookable"
---| "seamlessplayerswapper"
---| "searchable"
---| "seasons"
---| "selfstacker"
---| "sentientaxe"
---| "setbonus"
---| "sewing"
---| "shadowcreaturespawner"
---| "shadowdominance"
---| "shadowhandspawner"
---| "shadowlevel"
---| "shadowsubmissive"
---| "shadowthrallmanager"
---| "shardstate"
---| "shard_autosaver"
---| "shard_clock"
---| "shard_daywalkerspawner"
---| "shard_mermkingwatcher"
---| "shard_players"
---| "shard_seasons"
---| "shard_sinkholes"
---| "shard_worldreset"
---| "shard_worldvoter"
---| "sharkboimanager"
---| "sharkboimanagerhelper"
---| "sharklistener"
---| "shatterfx"
---| "shaveable"
---| "shaver"
---| "shedder"
---| "shelf"
---| "sheltered"
---| "shop"
---| "simplebook"
---| "simplemagicgrower"
---| "singable"
---| "singinginspiration"
---| "singingshellmanager"
---| "singingshelltrigger"
---| "sinkholespawner"
---| "sisturnregistry"
---| "sittable"
---| "sizetweener"
---| "skeletonsweeper"
---| "skilltreeupdater"
---| "skinner"
---| "skinner_beefalo"
---| "sleeper"
---| "sleepingbag"
---| "sleepingbaguser"
---| "slipperyfeet"
---| "slipperyfeettarget"
---| "smotherer"
---| "soul"
---| "souleater"
---| "spawner"
---| "spawnfader"
---| "spdamageutil"
---| "specialeventsetup"
---| "spectatorcorpse"
---| "spell"
---| "spellbook"
---| "spellbookcooldowns"
---| "spellcaster"
---| "spidermutator"
---| "spooked"
---| "squadmember"
---| "squidspawner"
---| "stackable"
---| "staffsanity"
---| "stageactingprop"
---| "stageactor"
---| "steeringwheel"
---| "steeringwheeluser"
---| "stewer"
---| "stormwatcher"
---| "storyteller"
---| "storytellingprop"
---| "strafer"
---| "stretcher"
---| "strongman"
---| "stuckdetection"
---| "stunnable"
---| "submersible"
---| "summoningitem"
---| "symbolswapdata"
---| "tackler"
---| "tacklesketch"
---| "talkable"
---| "talker"
---| "targettracker"
---| "teacher"
---| "teamattacker"
---| "teamleader"
---| "teleportedoverride"
---| "teleporter"
---| "temperature"
---| "temperatureoverrider"
---| "terraformer"
---| "thief"
---| "timer"
---| "toadstoolspawner"
---| "toggleableitem"
---| "tool"
---| "touchstonetracker"
---| "townportalregistry"
---| "tradable"
---| "trader"
---| "transformer"
---| "transparentonsanity"
---| "trap"
---| "treasuremarked"
---| "treegrowthsolution"
---| "tributable"
---| "trophyscale"
---| "uianim"
---| "undertile"
---| "unevenground"
---| "uniqueid"
---| "uniqueprefabids"
---| "unsaddler"
---| "unwrappable"
---| "updatelooper"
---| "upgradeable"
---| "upgrademodule"
---| "upgrademoduleowner"
---| "upgrademoduleremover"
---| "upgrader"
---| "useableitem"
---| "useabletargeteditem"
---| "vanish_on_sleep"
---| "vase"
---| "vasedecoration"
---| "vinebridgemanager"
---| "wagpunk_manager"
---| "walkableplatform"
---| "walkableplatformmanager"
---| "walkableplatformplayer"
---| "walkingplank"
---| "walkingplankuser"
---| "wardrobe"
---| "waterphysics"
---| "waterproofer"
---| "watersource"
---| "wateryprotection"
---| "wavemanager"
---| "wax"
---| "waxable"
---| "weapon"
---| "weather"
---| "weighable"
---| "werebeast"
---| "wereeater"
---| "wereness"
---| "wildfires"
---| "winch"
---| "winchtarget"
---| "winonateleportpadmanager"
---| "wintersfeasttable"
---| "wintertreegiftable"
---| "winter_treeseed"
---| "wisecracker"
---| "witherable"
---| "workable"
---| "worker"
---| "workmultiplier"
---| "worldcharacterselectlobby"
---| "worlddeciduoustreeupdater"
---| "worldmeteorshower"
---| "worldmigrator"
---| "worldoverseer"
---| "worldreset"
---| "worldsettings"
---| "worldsettingstimer"
---| "worldstate"
---| "worldtemperature"
---| "worldvoter"
---| "worldwind"
---| "writeable"
---| "yotb_sewer"
---| "yotb_skinunlocker"
---| "yotb_stagemanager"
---| "yotb_stager"
---| "yotc_racecompetitor"
---| "yotc_raceprizemanager"
---| "yotc_racestart"
---| "yotc_racestats"
---| "yotd_raceprizemanager"
