---@diagnostic disable: lowercase-global, undefined-global, trailing-space

---@meta

---
---UNKNOWN
---
---所在文件: scripts/debugkeys.lua
function DoReload()
end

---
---UNKNOWN
---
---所在文件: scripts/debugkeys.lua
function d_gettiles()
end

---
---UNKNOWN
---
---@param key idk 
---@param fn idk 
---@param down idk 
---所在文件: scripts/debugkeys.lua
function AddGameDebugKey(key,fn,down)
end

---
---UNKNOWN
---
---所在文件: scripts/debugkeys.lua
function SimBreakPoint()
end

---
---UNKNOWN
---
---@param button idk 
---@param down idk 
---@param x idk 
---@param y idk 
---所在文件: scripts/debugkeys.lua
function DoDebugMouse(button,down,x,y)
end

---
---UNKNOWN
---
---@param key idk 
---@param down idk 
---所在文件: scripts/debugkeys.lua
function DoDebugKey(key,down)
end

---
---UNKNOWN
---
---所在文件: scripts/debugkeys.lua
function d_addemotekeys()
end

---
---UNKNOWN
---
---@param key idk 
---@param fn idk 
---@param down idk 
---所在文件: scripts/debugkeys.lua
function AddGlobalDebugKey(key,fn,down)
end

---
---UNKNOWN
---
---所在文件: scripts/preloadsounds.lua
function PreloadSounds()
end

---
---UNKNOWN
---
---@param list idk 
---所在文件: scripts/preloadsounds.lua
function PreloadSoundList(list)
end

---
---UNKNOWN
---
---@param value idk 
---@param varname idk 
---@param fastmode idk 
---@param ident idk 
---所在文件: scripts/dumper.lua
function DataDumper(value,varname,fastmode,ident)
end

---
---UNKNOWN
---
---@param value idk 
---@param ident idk 
---@param path idk 
---所在文件: scripts/dumper.lua
function dumplua(value,ident,path)
end

---
---UNKNOWN
---
---@param image_widget idk 
---@param character idk 
---所在文件: scripts/characterutil.lua
function SetOvalPortraitTexture(image_widget,character)
end

---
---UNKNOWN
---
---@param character idk 
---@param skin idk 
---所在文件: scripts/characterutil.lua
function GetCharacterTitle(character,skin)
end

---
---UNKNOWN
---
---@param character idk 
---@param with_bonus_items idk 
---所在文件: scripts/characterutil.lua
function GetUniquePotentialCharacterStartingInventoryItems(character,with_bonus_items)
end

---
---UNKNOWN
---
---@param character idk 
---所在文件: scripts/characterutil.lua
function GetCharacterAvatarTextureLocation(character)
end

---
---UNKNOWN
---
---@param image_widget idk 
---@param character idk 
---所在文件: scripts/characterutil.lua
function SetHeroNameTexture_Gold(image_widget,character)
end

---
---UNKNOWN
---
---@param image_widget idk 
---@param character idk 
---@param skin idk 
---所在文件: scripts/characterutil.lua
function SetSkinnedOvalPortraitTexture(image_widget,character,skin)
end

---
---UNKNOWN
---
---@param data idk 
---所在文件: scripts/characterutil.lua
function GetKilledByFromMorgueRow(data)
end

---
---UNKNOWN
---
---@param image_widget idk 
---@param character idk 
---所在文件: scripts/characterutil.lua
function SetHeroNameTexture_Grey(image_widget,character)
end

---
---UNKNOWN
---
---@param base idk 
---@param _ctor idk 
---@param props idk 
---@return table
---所在文件: scripts/class.lua
function Class(base,_ctor,props)
end

---
---UNKNOWN
---
---@param t idk 
---@param k idk 
---所在文件: scripts/class.lua
function makereadonly(t,k)
end

---
---UNKNOWN
---
---@param mt idk 
---所在文件: scripts/class.lua
function ReloadedClass(mt)
end

---
---UNKNOWN
---
---所在文件: scripts/class.lua
function HandleClassInstanceTracking()
end

---
---UNKNOWN
---
---@param t idk 
---@param k idk 
---@param fn idk 
---所在文件: scripts/class.lua
function addsetter(t,k,fn)
end

---
---UNKNOWN
---
---@param t idk 
---@param k idk 
---所在文件: scripts/class.lua
function removesetter(t,k)
end

---
---UNKNOWN
---
---@param asset_table idk 
---@param font_table idk 
---所在文件: scripts/fonthelper.lua
function AddFontAssets(asset_table,font_table)
end

---
---UNKNOWN
---
---所在文件: scripts/maputil.lua
function ShowClosestNodeToPlayer()
end

---
---UNKNOWN
---
---@param points idk 
---所在文件: scripts/maputil.lua
function convexHull(points)
end

---
---UNKNOWN
---
---@param graph idk 
---所在文件: scripts/maputil.lua
function DrawWalkableGrid(graph)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---所在文件: scripts/maputil.lua
function GetClosestNode(x,y)
end

---
---UNKNOWN
---
---@param graph idk 
---所在文件: scripts/maputil.lua
function ReconstructTopology(graph)
end

---
---UNKNOWN
---
---@param graph idk 
---所在文件: scripts/maputil.lua
function ShowWalkableGrid(graph)
end

---
---UNKNOWN
---
---@param count idk 
---所在文件: scripts/maputil.lua
function PlayerSub(count)
end

---
---UNKNOWN
---
---所在文件: scripts/maputil.lua
function GetClosestNodeToPlayer()
end

---
---UNKNOWN
---
---@param node idk 
---@param numnodes idk 
---所在文件: scripts/maputil.lua
function GrabSubGraphAroundNode(node,numnodes)
end

---
---UNKNOWN
---
---所在文件: scripts/maputil.lua
function MapHideAll()
end

---
---UNKNOWN
---
---@param o idk 
---@param a idk 
---@param b idk 
---所在文件: scripts/maputil.lua
function cross(o,a,b)
end

---
---UNKNOWN
---
---@param popupcode idk 
---@param mod_name idk 
---所在文件: scripts/popupmanager.lua
function GetPopupIDFromPopupCode(popupcode,mod_name)
end

---
---UNKNOWN
---
---@param popupcode idk 
---@param mod_name idk 
---所在文件: scripts/popupmanager.lua
function GetPopupFromPopupCode(popupcode,mod_name)
end

---
---UNKNOWN
---
---@param pos idk 
---@param rotation idk 
---@param rmin idk 
---@param rmax idk 
---@param riter idk 
---@param validwalkablefn idk 
---所在文件: scripts/util.lua
function ControllerReticle_Blink_GetPosition_Oneshot(pos,rotation,rmin,rmax,riter,validwalkablefn)
end

---
---UNKNOWN
---
---@param p idk 
---@param v1 idk 
---@param v2 idk 
---所在文件: scripts/util.lua
function DistPointToSegmentXYSq(p,v1,v2)
end

---
---UNKNOWN
---
---@param filepath idk 
---@param force_path_search idk 
---@param search_first_path idk 
---所在文件: scripts/util.lua
function resolvefilepath_soft(filepath,force_path_search,search_first_path)
end

---
---UNKNOWN
---
---@param x idk 
---@param b idk 
---所在文件: scripts/util.lua
function clearbit(x,b)
end

---
---UNKNOWN
---
---@param hex idk 
---所在文件: scripts/util.lua
function HexToRGB(hex)
end

---
---UNKNOWN
---
---@param choices idk 
---所在文件: scripts/util.lua
function GetRandomItemWithIndex(choices)
end

---
---UNKNOWN
---
---@param x idk 
---所在文件: scripts/util.lua
function isnan(x)
end

---
---UNKNOWN
---
---所在文件: scripts/util.lua
function TrackMem()
end

---
---UNKNOWN
---
---@param prefab idk 
---所在文件: scripts/util.lua
function DebugSpawn(prefab)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/util.lua
function JoinArrays(...)
end

---
---UNKNOWN
---
---@param current idk 
---@param basedelta idk 
---所在文件: scripts/util.lua
function CalcDiminishingReturns(current,basedelta)
end

---
---UNKNOWN
---
---@param tab idk 
---所在文件: scripts/util.lua
function PrintTable(tab)
end

---
---UNKNOWN
---
---@param ret idk 
---@param ... idk 
---所在文件: scripts/util.lua
function ConcatArrays(ret,...)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/util.lua
function MergeMaps(...)
end

---
---UNKNOWN
---
---@param fn idk 
---@param fnenv idk 
---所在文件: scripts/util.lua
function RunInEnvironmentSafe(fn,fnenv)
end

---
---UNKNOWN
---
---@param tree idk 
---@param unique idk 
---所在文件: scripts/util.lua
function FlattenTree(tree,unique)
end

---
---UNKNOWN
---
---@param t idk 
---@param k idk 
---@param ... idk 
---所在文件: scripts/util.lua
function metanext(t,k,...)
end

---
---UNKNOWN
---
---@param t idk 
---所在文件: scripts/util.lua
function ipairs_reverse(t)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/util.lua
function MergeKeyValueList(...)
end

---
---UNKNOWN
---
---@param prefab idk 
---@param loc idk 
---@param scale idk 
---@param offset idk 
---所在文件: scripts/util.lua
function SpawnAt(prefab,loc,scale,offset)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/util.lua
function ArrayUnion(...)
end

---
---UNKNOWN
---
---@param t idk 
---所在文件: scripts/util.lua
function orderedPairs(t)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/util.lua
function MergeMapsDeep(...)
end

---
---UNKNOWN
---
---@param num idk 
---@param choices idk 
---所在文件: scripts/util.lua
function PickSomeWithDups(num,choices)
end

---
---UNKNOWN
---
---@param untrusted_code idk 
---所在文件: scripts/util.lua
function RunInSandbox(untrusted_code)
end

---
---UNKNOWN
---
---@param t idk 
---@param ... idk 
---所在文件: scripts/util.lua
function metaipairs(t,...)
end

---
---UNKNOWN
---
---@param t idk 
---所在文件: scripts/util.lua
function rawstring(t)
end

---
---UNKNOWN
---
---@param orig idk 
---@param dest idk 
---所在文件: scripts/util.lua
function shallowcopy(orig,dest)
end

---
---UNKNOWN
---
---@param dict idk 
---所在文件: scripts/util.lua
function sortedKeys(dict)
end

---
--- 获取一个在基准值附近带有随机变化的数值
---
---@param baseval number # 基准值
---@param randomval number # 随机变化的最大范围（正负值）
---@return number # 在 [baseval - randomval, baseval + randomval] 范围内的随机数值
---所在文件: scripts/util.lua
---author: lan
function GetRandomWithVariance(baseval,randomval)
end

---
---UNKNOWN
---
---@param position idk 
---@param forward idk 
---@param width idk 
---@param testPos idk 
---所在文件: scripts/util.lua
function IsWithinAngle(position,forward,width,testPos)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/util.lua
function ControllerPlacer_Boat_SpotFinder(inst)
end

---
---UNKNOWN
---
---@param input idk 
---@param array idk 
---所在文件: scripts/util.lua
function StringContainsAnyInArray(input,array)
end

---
---UNKNOWN
---
---@param t idk 
---@param value idk 
---所在文件: scripts/util.lua
function RemoveByValue(t,value)
end

---
---UNKNOWN
---
---@param object idk 
---所在文件: scripts/util.lua
function deepcopy(object)
end

---
---UNKNOWN
---
---@param placer idk 
---@param player idk 
---@param ox idk 
---@param oy idk 
---@param oz idk 
---所在文件: scripts/util.lua
function ControllerPlacer_Boat_SpotFinder_Internal(placer,player,ox,oy,oz)
end

---
---UNKNOWN
---
---所在文件: scripts/util.lua
function GetMemoizedFilePaths()
end

---
---UNKNOWN
---
---@param untrusted_code idk 
---@param error_handler idk 
---所在文件: scripts/util.lua
function RunInSandboxSafeCatchInfiniteLoops(untrusted_code,error_handler)
end

---
---UNKNOWN
---
---@param pos idk 
---@param rotation idk 
---@param maxrange idk 
---@param validwalkablefn idk 
---所在文件: scripts/util.lua
function ControllerReticle_Blink_GetPosition_Direction(pos,rotation,maxrange,validwalkablefn)
end

---
---UNKNOWN
---
---所在文件: scripts/util.lua
function mem_report()
end

---
---UNKNOWN
---
---@param player idk 
---@param validwalkablefn idk 
---所在文件: scripts/util.lua
function ControllerReticle_Blink_GetPosition(player,validwalkablefn)
end

---
---UNKNOWN
---
---@param t idk 
---所在文件: scripts/util.lua
function IsTableEmpty(t)
end

---
---UNKNOWN
---
---@param err idk 
---所在文件: scripts/util.lua
function generic_error(err)
end

---
---UNKNOWN
---
---@param array idk 
---所在文件: scripts/util.lua
function shuffleArray(array)
end

---
---UNKNOWN
---
---@param t idk 
---@param outstr idk 
---@param indent idk 
---所在文件: scripts/util.lua
function dumpinternal(t,outstr,indent)
end

---
---UNKNOWN
---
---@param v1 idk 
---@param v2 idk 
---@param v3 idk 
---@param v4 idk 
---所在文件: scripts/util.lua
function distsq(v1,v2,v3,v4)
end

---
---UNKNOWN
---
---@param choices idk 
---所在文件: scripts/util.lua
function GetRandomKey(choices)
end

---
---UNKNOWN
---
---@param text idk 
---@param text_filter_context idk 
---@param net_id idk 
---所在文件: scripts/util.lua
function ApplyLocalWordFilter(text,text_filter_context,net_id)
end

---
---UNKNOWN
---
---@param x idk 
---@param b idk 
---所在文件: scripts/util.lua
function checkbit(x,b)
end

---
---UNKNOWN
---
---@param x idk 
---所在文件: scripts/util.lua
function isbadnumber(x)
end

---
---UNKNOWN
---
---@param data idk 
---所在文件: scripts/util.lua
function DecodeAndUnzipSaveData(data)
end

---
---UNKNOWN
---
---@param str idk 
---所在文件: scripts/util.lua
function DecodeAndUnzipString(str)
end

---
---UNKNOWN
---
---@param task idk 
---所在文件: scripts/util.lua
function GetTaskTime(task)
end

---
---UNKNOWN
---
---@param data idk 
---所在文件: scripts/util.lua
function ZipAndEncodeSaveData(data)
end

---
---UNKNOWN
---
---@param object idk 
---所在文件: scripts/util.lua
function deepcopynometa(object)
end

---
---UNKNOWN
---
---@param data idk 
---所在文件: scripts/util.lua
function ZipAndEncodeString(data)
end

---
---UNKNOWN
---
---@param t idk 
---@param k idk 
---所在文件: scripts/util.lua
function metarawget(t,k)
end

---
---UNKNOWN
---
---@param t idk 
---@param name idk 
---所在文件: scripts/util.lua
function DumpTableXML(t,name)
end

---
---UNKNOWN
---
---@param t idk 
---@param k idk 
---@param v idk 
---所在文件: scripts/util.lua
function metarawset(t,k,v)
end

---
---范围内随机数
---
---@param min number 
---@param max number 
---@return number
---@nodiscard
---所在文件: scripts/util.lua
---
---author: lan
function GetRandomMinMax(min,max)
    return min + math.random()*(max - min)
end

---
---UNKNOWN
---
---@param target idk 
---@param entities idk 
---所在文件: scripts/util.lua
function GetClosest(target,entities)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/util.lua
function ArrayIntersection(...)
end

---
---UNKNOWN
---
---@param t idk 
---@param state idk 
---所在文件: scripts/util.lua
function orderedNext(t,state)
end

---
---UNKNOWN
---
---@param x idk 
---@param b idk 
---所在文件: scripts/util.lua
function setbit(x,b)
end

---
---UNKNOWN
---
---@param base idk 
---@param subtract idk 
---所在文件: scripts/util.lua
function SubtractMapKeys(base,subtract)
end

---
---UNKNOWN
---
---@param t idk 
---所在文件: scripts/util.lua
function __genOrderedIndex(t)
end

---
---UNKNOWN
---
---@param orig idk 
---@param addition idk 
---@param mult idk 
---所在文件: scripts/util.lua
function ExtendedArray(orig,addition,mult)
end

---
---UNKNOWN
---
---@param fn idk 
---@param fnenv idk 
---所在文件: scripts/util.lua
function RunInEnvironment(fn,fnenv)
end

---
---UNKNOWN
---
---@param num idk 
---@param choices idk 
---所在文件: scripts/util.lua
function PickSome(num,choices)
end

---
---UNKNOWN
---
---@param target_tick idk 
---所在文件: scripts/util.lua
function GetTimeForTick(target_tick)
end

---
---UNKNOWN
---
---@param untrusted_code idk 
---@param error_handler idk 
---所在文件: scripts/util.lua
function RunInSandboxSafe(untrusted_code,error_handler)
end

---
---UNKNOWN
---
---@param dict idk 
---所在文件: scripts/util.lua
function shuffledKeys(dict)
end

---
---UNKNOWN
---
---@param x idk 
---所在文件: scripts/util.lua
function isinf(x)
end

---
---UNKNOWN
---
---@param count idk 
---@param index idk 
---所在文件: scripts/util.lua
function circular_index_number(count,index)
end

---
---UNKNOWN
---
---@param choices idk 
---@return idk
---所在文件: scripts/util.lua
function weighted_random_choice(choices)
end

---
---UNKNOWN
---
---@param tracking_data idk 
---@param function_ptr idk 
---@param function_data idk 
---所在文件: scripts/util.lua
function TrackedAssert(tracking_data,function_ptr,function_data)
end

---
---UNKNOWN
---
---@param func_or_val idk 
---@param ... idk 
---所在文件: scripts/util.lua
function FunctionOrValue(func_or_val,...)
end

---
---UNKNOWN
---
---@param value idk 
---所在文件: scripts/util.lua
function fastdump(value)
end

---
---UNKNOWN
---
---@param r idk 
---@param g idk 
---@param b idk 
---所在文件: scripts/util.lua
function RGBToPercentColor(r,g,b)
end

---
---UNKNOWN
---
---@param tSource idk 
---@param tException idk 
---所在文件: scripts/util.lua
function ExceptionArrays(tSource,tException)
end

---
---UNKNOWN
---
---@param boat idk 
---@param x idk 
---@param z idk 
---所在文件: scripts/util.lua
function GetAngleFromBoat(boat,x,z)
end

---
---UNKNOWN
---
---@param segments idk 
---@param radius idk 
---@param base_pt idk 
---@param pt idk 
---@param angle idk 
---所在文件: scripts/util.lua
function GetCircleEdgeSnapTransform(segments,radius,base_pt,pt,angle)
end

---
---UNKNOWN
---
---@param table idk 
---所在文件: scripts/util.lua
function GetTableSize(table)
end

---
---UNKNOWN
---
---@param inst idk 
---@param boat idk 
---@param override_pt idk 
---所在文件: scripts/util.lua
function SnapToBoatEdge(inst,boat,override_pt)
end

---
---UNKNOWN
---
---@param t idk 
---@param fn idk 
---所在文件: scripts/util.lua
function sorted_pairs(t,fn)
end

---
---UNKNOWN
---
---@param tab idk 
---所在文件: scripts/util.lua
function GetFlattenedSparse(tab)
end

---
---UNKNOWN
---
---@param hex idk 
---所在文件: scripts/util.lua
function HexToPercentColor(hex)
end

---
---UNKNOWN
---
---@param object idk 
---所在文件: scripts/util.lua
function cleartable(object)
end

---
---UNKNOWN
---
---@param memoized_file_paths idk 
---所在文件: scripts/util.lua
function SetMemoizedFilePaths(memoized_file_paths)
end

---
---UNKNOWN
---
---@param choices idk 
---所在文件: scripts/util.lua
function GetRandomItem(choices)
end

---
---UNKNOWN
---
---@param filepath idk 
---@param force_path_search idk 
---@param search_first_path idk 
---所在文件: scripts/util.lua
function softresolvefilepath(filepath,force_path_search,search_first_path)
end

---
---UNKNOWN
---
---@param t idk 
---@param index idk 
---所在文件: scripts/util.lua
function circular_index(t,index)
end

---
---UNKNOWN
---
---@param p1 idk 
---@param p2 idk 
---所在文件: scripts/util.lua
function Dist2dSq(p1,p2)
end

---
---UNKNOWN
---
---@param task idk 
---所在文件: scripts/util.lua
function GetTaskRemaining(task)
end

---
---UNKNOWN
---
---@param target_time idk 
---所在文件: scripts/util.lua
function GetTickForTime(target_time)
end

---
---UNKNOWN
---
---@param choices idk 
---@param num_choices idk 
---所在文件: scripts/util.lua
function weighted_random_choices(choices,num_choices)
end

---
---UNKNOWN
---
---所在文件: scripts/util.lua
function DumpMem()
end

---
---UNKNOWN
---
---@param filepath idk 
---@param force_path_search idk 
---@param search_first_path idk 
---@return string
---所在文件: scripts/util.lua
function resolvefilepath(filepath,force_path_search,search_first_path)
end

---
---UNKNOWN
---
---@param t idk 
---@param ... idk 
---所在文件: scripts/util.lua
function metapairs(t,...)
end

---
---UNKNOWN
---
---@param cond idk 
---@param name idk 
---@param node idk 
---所在文件: scripts/behaviourtree.lua
function WhileNode(cond,name,node)
end

---
---UNKNOWN
---
---@param cond idk 
---@param name idk 
---@param node idk 
---所在文件: scripts/behaviourtree.lua
function IfNode(cond,name,node)
end

---
---UNKNOWN
---
---@param ifcond idk 
---@param whilecond idk 
---@param name idk 
---@param node idk 
---所在文件: scripts/behaviourtree.lua
function IfThenDoWhileNode(ifcond,whilecond,name,node)
end

---
---UNKNOWN
---
---@param tbl idk 
---所在文件: scripts/translator.lua
function TranslateStringTable(tbl)
end

---
---UNKNOWN
---
---@param name idk 
---@param age idk 
---@param build idk 
---所在文件: scripts/consolecommands.lua
function c_kitcoon(name,age,build)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_repeatlastcommand()
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/consolecommands.lua
function c_printtextureinfo(filename)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_sel()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_makeboatspiral()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_dump()
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_setmightiness(n)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_goadventuring(player)
end

---
---UNKNOWN
---
---@param fnstr idk 
---所在文件: scripts/consolecommands.lua
function c_remote(fnstr)
end

---
---UNKNOWN
---
---@param str idk 
---所在文件: scripts/consolecommands.lua
function c_searchprefabs(str)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_despawn(player)
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_setwereness(n)
end

---
---通过GUID查找并获取实体
---
---@param guid number # GUID 
---@return ent|nil # 实体
---@nodiscard
---所在文件: scripts/consolecommands.lua
---
---author: lan
function c_inst(guid)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_regenerateworld()
end

---
---UNKNOWN
---
---@param radius idk 
---@param parent idk 
---所在文件: scripts/consolecommands.lua
function c_showradius(radius,parent)
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_setinspiration(n)
end

---
---UNKNOWN
---
---@param delta idk 
---所在文件: scripts/consolecommands.lua
function c_rotateccw(delta)
end

---
---UNKNOWN
---
---@param player idk 
---@param percent idk 
---所在文件: scripts/consolecommands.lua
function c_maintainhunger(player,percent)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_spawnrift()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_record()
end

---
---UNKNOWN
---
---@param prefab PrefabID 
---@param count idk 
---@param dontselect idk 
---@return ent
---所在文件: scripts/consolecommands.lua
function c_spawn(prefab,count,dontselect)
end

---
---UNKNOWN
---
---@param count idk 
---所在文件: scripts/consolecommands.lua
function c_rollback(count)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_pos(inst)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_groundtype()
end

---
---UNKNOWN
---
---@param angle idk 
---所在文件: scripts/consolecommands.lua
function c_setrotation(angle)
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_settemperature(n)
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_setmoisture(n)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_getnumplayers()
end

---
---获取指针位置的世界坐标(客户端)
---
---@return Vector3 指针位置的世界坐标
---所在文件: scripts/consolecommands.lua
---@nodiscard
function ConsoleWorldPosition()
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_armor(player)
end

---
---UNKNOWN
---
---@param song idk 
---@param startpos idk 
---@param placementfn idk 
---@param spacing_multiplier idk 
---@param out_of_range_mode idk 
---所在文件: scripts/consolecommands.lua
function c_shellsfromtable(song,startpos,placementfn,spacing_multiplier,out_of_range_mode)
end

---
---UNKNOWN
---
---@param key idk 
---所在文件: scripts/consolecommands.lua
function c_knownassert(key)
end

---
---UNKNOWN
---
---@param phase idk 
---所在文件: scripts/consolecommands.lua
function c_simphase(phase)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_autoteleportplayers()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_listplayers()
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/consolecommands.lua
function c_removeall(name)
end

---
---UNKNOWN
---
---@param entity idk 
---所在文件: scripts/consolecommands.lua
function c_remove(entity)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_boatcollision()
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_sethunger(n)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_listallplayers()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_makecrabboat()
end

---
---UNKNOWN
---
---@param recname idk 
---所在文件: scripts/consolecommands.lua
function c_mat(recname)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_supergodmode(player)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_reset()
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_printpos(inst)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/consolecommands.lua
function c_gonext(name)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_mermthrone()
end

---
---UNKNOWN
---
---@param player idk 
---@param percent idk 
---所在文件: scripts/consolecommands.lua
function c_maintainmoisture(player,percent)
end

---
---UNKNOWN
---
---@param prefab idk 
---所在文件: scripts/consolecommands.lua
function c_giveingredients(prefab)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_makeboat()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_allbooks()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_stopvote()
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_sethealth(n)
end

---
---UNKNOWN
---
---@param multiplier idk 
---所在文件: scripts/consolecommands.lua
function c_speedmult(multiplier)
end

---
---UNKNOWN
---
---@param scenario idk 
---所在文件: scripts/consolecommands.lua
function c_doscenario(scenario)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_reregisterportals()
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_setsanity(n)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_debugshards()
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_setminhealth(n)
end

---
---UNKNOWN
---
---@param worldId idk 
---@param portalId idk 
---所在文件: scripts/consolecommands.lua
function c_migrateto(worldId,portalId)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_sounddebugui()
end

---
---UNKNOWN
---
---@param prefab idk 
---@param radius idk 
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_findnext(prefab,radius,inst)
end

---
---UNKNOWN
---
---@param ip idk 
---@param port idk 
---@param password idk 
---所在文件: scripts/consolecommands.lua
function c_connect(ip,port,password)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_sounddebug()
end

---
---UNKNOWN
---
---@param commandname idk 
---@param playeroruserid idk 
---所在文件: scripts/consolecommands.lua
function c_startvote(commandname,playeroruserid)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function ConsoleCommandPlayer()
end

---
---UNKNOWN
---
---@param prefab idk 
---@param radius idk 
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_find(prefab,radius,inst)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_dumpentities()
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_move(inst)
end

---
---UNKNOWN
---
---@param unique idk 
---所在文件: scripts/consolecommands.lua
function c_forcecrash(unique)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/consolecommands.lua
function c_removeat(x,y,z)
end

---
---UNKNOWN
---
---@param prefab idk 
---所在文件: scripts/consolecommands.lua
function c_list(prefab)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function ConsoleWorldEntityUnderMouse()
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_freecrafting(player)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_startinggear(player)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_countallprefabs()
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/consolecommands.lua
function c_removeallwithtags(...)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_cancelmaintaintasks(player)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_summonbearger()
end

---
---UNKNOWN
---
---@param msg idk 
---@param interval idk 
---@param category idk 
---所在文件: scripts/consolecommands.lua
function c_announce(msg,interval,category)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_armour(player)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_mermking()
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_teleport(x,y,z,inst)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_makegrassboat()
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_select(inst)
end

---
---UNKNOWN
---
---@param player idk 
---@param temp idk 
---所在文件: scripts/consolecommands.lua
function c_maintaintemperature(player,temp)
end

---
---UNKNOWN
---
---@param player idk 
---@param percent idk 
---所在文件: scripts/consolecommands.lua
function c_maintainsanity(player,percent)
end

---
---UNKNOWN
---
---@param dest idk 
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_goto(dest,inst)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_godmode(player)
end

---
---UNKNOWN
---
---@param delta idk 
---所在文件: scripts/consolecommands.lua
function c_rotatecw(delta)
end

---
---UNKNOWN
---
---@param n idk 
---所在文件: scripts/consolecommands.lua
function c_addelectricity(n)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_speedup()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_gatherplayers()
end

---
---UNKNOWN
---
---@param wipesettings idk 
---所在文件: scripts/consolecommands.lua
function c_regenerateshard(wipesettings)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_summonmalbatross()
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/consolecommands.lua
function c_maintainall(player)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_summondeerclops()
end

---
---UNKNOWN
---
---@param save idk 
---所在文件: scripts/consolecommands.lua
function c_shutdown(save)
end

---
---UNKNOWN
---
---@param prefab idk 
---@param rad idk 
---所在文件: scripts/consolecommands.lua
function c_selectnear(prefab,rad)
end

---
---UNKNOWN
---
---@param num idk 
---所在文件: scripts/consolecommands.lua
function c_skip(num)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_makeinvisible()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_worldstatedebug()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_dumpworldstate()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_dumpseasons()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_sel_health()
end

---
---UNKNOWN
---
---@param tag idk 
---@param noprint idk 
---所在文件: scripts/consolecommands.lua
function c_counttagged(tag,noprint)
end

---
---UNKNOWN
---
---@param tag idk 
---所在文件: scripts/consolecommands.lua
function c_listtag(tag)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_getmaxplayers()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_tile()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_emptyworld()
end

---
---UNKNOWN
---
---@param prefab idk 
---@param noprint idk 
---所在文件: scripts/consolecommands.lua
function c_countprefabs(prefab,noprint)
end

---
---UNKNOWN
---
---@param tag idk 
---@param radius idk 
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_findtag(tag,radius,inst)
end

---
---UNKNOWN
---
---@param worldId idk 
---@param portalId idk 
---所在文件: scripts/consolecommands.lua
function c_migrationportal(worldId,portalId)
end

---
---UNKNOWN
---
---@param roomname idk 
---@param inst idk 
---所在文件: scripts/consolecommands.lua
function c_gotoroom(roomname,inst)
end

---
---UNKNOWN
---
---@param player idk 
---@param percent idk 
---所在文件: scripts/consolecommands.lua
function c_maintainhealth(player,percent)
end

---
---UNKNOWN
---
---@param prefab idk 
---@param count idk 
---@param dontselect idk 
---所在文件: scripts/consolecommands.lua
function c_equip(prefab,count,dontselect)
end

---
---UNKNOWN
---
---@param prefab PrefabID 
---@param count integer 
---@param dontselect idk 
---@return ent
---所在文件: scripts/consolecommands.lua
function c_give(prefab,count,dontselect)
end

---
---UNKNOWN
---
---@param songdata idk 
---@param overrides idk 
---@param dont_spawn_shells idk 
---所在文件: scripts/consolecommands.lua
function c_guitartab(songdata,overrides,dont_spawn_shells)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_save()
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function ResetControllersAndQuitGame()
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/consolecommands.lua
function c_selectnext(name)
end

---
---UNKNOWN
---
---所在文件: scripts/consolecommands.lua
function c_netstats()
end

---
---UNKNOWN
---
---@param complete_callback idk 
---所在文件: scripts/upsell.lua
function UpdateGamePurchasedState(complete_callback)
end

---
---UNKNOWN
---
---所在文件: scripts/upsell.lua
function UpsellShowing()
end

---
---UNKNOWN
---
---所在文件: scripts/upsell.lua
function HandleUpsellClose()
end

---
---UNKNOWN
---
---所在文件: scripts/upsell.lua
function IsGamePurchased()
end

---
---UNKNOWN
---
---@param shouldquit idk 
---所在文件: scripts/upsell.lua
function ShowUpsellScreen(shouldquit)
end

---
---UNKNOWN
---
---所在文件: scripts/upsell.lua
function CheckDemoTimeout()
end

---
---UNKNOWN
---
---@param dt idk 
---所在文件: scripts/upsell.lua
function CheckForUpsellTimeout(dt)
end

---
---UNKNOWN
---
---所在文件: scripts/upsell.lua
function WaitingForPurchaseState()
end

---
---UNKNOWN
---
---@param cooker idk 
---@param name idk 
---所在文件: scripts/cooking.lua
function IsModCookingProduct(cooker,name)
end

---
---UNKNOWN
---
---@param cooker idk 
---@param recipe idk 
---所在文件: scripts/cooking.lua
function AddRecipeCard(cooker,recipe)
end

---
---UNKNOWN
---
---@param cooker idk 
---@param ingdata idk 
---所在文件: scripts/cooking.lua
function GetCandidateRecipes(cooker,ingdata)
end

---
---UNKNOWN
---
---@param tuning_var idk 
---@param fn idk 
---@param tuning_value idk 
---所在文件: scripts/tuning.lua
function AddTuningModifier(tuning_var,fn,tuning_value)
end

---
---UNKNOWN
---
---@param overrides idk 
---所在文件: scripts/tuning.lua
function Tune(overrides)
end

---
---UNKNOWN
---
---@param modname idk 
---所在文件: scripts/reload.lua
function hotswap(modname)
end

---
---UNKNOWN
---
---所在文件: scripts/reload.lua
function MonkeyPatchClasses()
end

---
---UNKNOWN
---
---@param ispressed idk 
---所在文件: scripts/reload.lua
function ProbeReload(ispressed)
end

---
---UNKNOWN
---
---所在文件: scripts/reload.lua
function DoReload()
end

---
---UNKNOWN
---
---@param cls idk 
---@param inh idk 
---所在文件: scripts/reload.lua
function ScrubClass(cls,inh)
end

---
---UNKNOWN
---
---@param mt idk 
---所在文件: scripts/reload.lua
function MonkeyPatchClass(mt)
end

---
---加载资源
---
---@param asset_type asset_type 资源类型
---@param path string 路径
---@return asset # 资源
---@nodiscard
---所在文件: scripts/i_dont_know_path.lua
function Asset(asset_type,path)
end

---
---UNKNOWN
---
---@param class idk 
---@param ctor idk 
---所在文件: scripts/entityscriptproxy.lua
function ProxyClass(class,ctor)
end

---
---UNKNOWN
---
---@param obj idk 
---所在文件: scripts/entityscriptproxy.lua
function ProxyInstance(obj)
end

---
---UNKNOWN
---
---@param filename idk 
---@param root idk 
---@param tbl_dta idk 
---@param tbl_lkp idk 
---所在文件: scripts/createstringspo_dlc.lua
function CreateStringsPOTv1(filename,root,tbl_dta,tbl_lkp)
end

---
---UNKNOWN
---
---@param base_dta idk 
---@param tbl_dta idk 
---@param lkp_var idk 
---@param file idk 
---所在文件: scripts/createstringspo_dlc.lua
function PrintTranslatedStringTableV1(base_dta,tbl_dta,lkp_var,file)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/createstringspo_dlc.lua
function IsDLCEnabled(val)
end

---
---UNKNOWN
---
---@param filename idk 
---@param root idk 
---@param tbl_dta idk 
---@param tbl_lkp idk 
---所在文件: scripts/createstringspo_dlc.lua
function CreateStringsPOTv2(filename,root,tbl_dta,tbl_lkp)
end

---
---UNKNOWN
---
---@param err idk 
---所在文件: scripts/stacktrace.lua
function DoStackTrace(err)
end

---
---UNKNOWN
---
---@param err idk 
---所在文件: scripts/stacktrace.lua
function StackTrace(err)
end

---
---UNKNOWN
---
---@param res idk 
---@param level idk 
---所在文件: scripts/stacktrace.lua
function getdebuglocals(res,level)
end

---
---UNKNOWN
---
---所在文件: scripts/stacktrace.lua
function StackTraceToLog()
end

---
---UNKNOWN
---
---@param v idk 
---所在文件: scripts/stacktrace.lua
function SaveToString(v)
end

---
---UNKNOWN
---
---@param res idk 
---@param start idk 
---@param top idk 
---@param bottom idk 
---所在文件: scripts/stacktrace.lua
function getdebugstack(res,start,top,bottom)
end

---
---UNKNOWN
---
---@param dt idk 
---所在文件: scripts/shadeeffects.lua
function ShadeEffectUpdate(dt)
end

---
---UNKNOWN
---
---@param enable idk 
---所在文件: scripts/shadeeffects.lua
function EnableShadeRenderer(enable)
end

---
---UNKNOWN
---
---@param x idk 
---@param z idk 
---所在文件: scripts/shadeeffects.lua
function SpawnLeafCanopy(x,z)
end

---
---UNKNOWN
---
---@param id idk 
---所在文件: scripts/shadeeffects.lua
function DespawnLeafCanopy(id)
end

---
---UNKNOWN
---
---@param modname idk 
---所在文件: scripts/modindex.lua
function GetWorkshopIdNumber(modname)
end

---
---UNKNOWN
---
---@param modname idk 
---所在文件: scripts/modindex.lua
function ResolveModname(modname)
end

---
---UNKNOWN
---
---@param modname idk 
---所在文件: scripts/modindex.lua
function IsWorkshopMod(modname)
end

---
---UNKNOWN
---
---@param func idk 
---所在文件: scripts/debughelpers.lua
function DumpUpvalues(func)
end

---
---UNKNOWN
---
---@param ent idk 
---所在文件: scripts/debughelpers.lua
function DumpEntity(ent)
end

---
---UNKNOWN
---
---@param comp idk 
---所在文件: scripts/debughelpers.lua
function DumpComponent(comp)
end

---
---UNKNOWN
---
---@param fn idk 
---@param id idk 
---@param param idk 
---所在文件: scripts/scheduler.lua
function StartThread(fn,id,param)
end

---
---UNKNOWN
---
---@param tick idk 
---所在文件: scripts/scheduler.lua
function RunStaticScheduler(tick)
end

---
---UNKNOWN
---
---@param id idk 
---所在文件: scripts/scheduler.lua
function KillThreadsWithID(id)
end

---
---UNKNOWN
---
---@param task idk 
---所在文件: scripts/scheduler.lua
function KillThread(task)
end

---
---UNKNOWN
---
---@param tick idk 
---所在文件: scripts/scheduler.lua
function RunScheduler(tick)
end

---
---UNKNOWN
---
---所在文件: scripts/scheduler.lua
function StopAllThreads()
end

---
---UNKNOWN
---
---@param fn idk 
---@param id idk 
---@param param idk 
---所在文件: scripts/scheduler.lua
function StartStaticThread(fn,id,param)
end

---
---UNKNOWN
---
---所在文件: scripts/scheduler.lua
function Wake()
end

---
---UNKNOWN
---
---@param task idk 
---所在文件: scripts/scheduler.lua
function WakeTask(task)
end

---
---UNKNOWN
---
---所在文件: scripts/scheduler.lua
function Yield()
end

---
---UNKNOWN
---
---@param time idk 
---所在文件: scripts/scheduler.lua
function Sleep(time)
end

---
---UNKNOWN
---
---所在文件: scripts/scheduler.lua
function Hibernate()
end

---
---UNKNOWN
---
---所在文件: scripts/mods.lua
function AreClientModsDisabled()
end

---
---UNKNOWN
---
---所在文件: scripts/mods.lua
function GetEnabledServerModsConfigData()
end

---
---UNKNOWN
---
---@param mod_name idk 
---@param mod_info_use idk 
---所在文件: scripts/mods.lua
function GetModVersion(mod_name,mod_info_use)
end

---
---UNKNOWN
---
---所在文件: scripts/mods.lua
function GetEnabledModNamesDetailed()
end

---
---UNKNOWN
---
---所在文件: scripts/mods.lua
function GetEnabledModsModInfoDetails()
end

---
---导入指定lua文件中的所有代码
---
---@param modulename string lua文件路径
---所在文件: scripts/mods.lua
function modimport(modulename)
end

---
---UNKNOWN
---
---@param mod_name idk 
---所在文件: scripts/mods.lua
function GetModFancyName(mod_name)
end

---
---UNKNOWN
---
---所在文件: scripts/mods.lua
function AreServerModsEnabled()
end

---
---UNKNOWN
---
---@param modname idk 
---@param isworldgen idk 
---@param isfrontend idk 
---所在文件: scripts/mods.lua
function CreateEnvironment(modname,isworldgen,isfrontend)
end

---
---UNKNOWN
---
---所在文件: scripts/mods.lua
function AreAnyModsEnabled()
end

---
---UNKNOWN
---
---所在文件: scripts/mods.lua
function AreAnyClientModsEnabled()
end

---
---UNKNOWN
---
---@param radius idk 
---所在文件: scripts/emitters.lua
function CreateDiscEmitter(radius)
end

---
---UNKNOWN
---
---所在文件: scripts/emitters.lua
function UnitRand()
end

---
---随机点生成器: 球面(返回的是一个随机点生成函数)
---
---@param radius number 
---@return fun():number,number,number
---@nodiscard
---所在文件: scripts/emitters.lua
function CreateSphereEmitter(radius)
end

---
---UNKNOWN
---
---@param x_min idk 
---@param y_min idk 
---@param z_min idk 
---@param x_max idk 
---@param y_max idk 
---@param z_max idk 
---所在文件: scripts/emitters.lua
function CreateBoxEmitter(x_min,y_min,z_min,x_max,y_max,z_max)
end

---
---UNKNOWN
---
---@param tris idk 
---@param scale idk 
---所在文件: scripts/emitters.lua
function Create2DTriEmitter(tris,scale)
end

---
---随机点生成器: 环上(返回的是一个随机点生成函数)
---
---@param radius number 
---@return fun():number,number
---@nodiscard
---所在文件: scripts/emitters.lua
function CreateRingEmitter(radius)
end

---
---UNKNOWN
---
---@param polygon idk 
---@param centroid idk 
---所在文件: scripts/emitters.lua
function CreateAreaEmitter(polygon,centroid)
end

---
---随机点生成器: 圆形(返回的是一个随机点生成函数)
---
---@param radius number 
---@return fun():number,number
---@nodiscard
---所在文件: scripts/emitters.lua
function CreateCircleEmitter(radius)
end

---
---UNKNOWN
---
---@param name idk 
---@param prefab_to_deploy idk 
---@param bank idk 
---@param build idk 
---@param anim idk 
---@param assets idk 
---@param floatable_data idk 
---@param tags idk 
---@param burnable idk 
---@param deployable_data idk 
---@param stack_size idk 
---@param PostMasterSimfn idk 
---所在文件: scripts/prefabutil.lua
function MakeDeployableKitItem(name,prefab_to_deploy,bank,build,anim,assets,floatable_data,tags,burnable,deployable_data,stack_size,PostMasterSimfn)
end

---
---UNKNOWN
---
---@param name idk 
---@param bank idk 
---@param build idk 
---@param anim idk 
---@param onground idk 
---@param snap idk 
---@param metersnap idk 
---@param scale idk 
---@param fixedcameraoffset idk 
---@param facing idk 
---@param postinit_fn idk 
---@param offset idk 
---@param onfailedplacement idk 
---所在文件: scripts/prefabutil.lua
function MakePlacer(name,bank,build,anim,onground,snap,metersnap,scale,fixedcameraoffset,facing,postinit_fn,offset,onfailedplacement)
end

---
---UNKNOWN
---
---@param bossprefab idk 
---@param shardid idk 
---所在文件: scripts/shardnetworking.lua
function Shard_SyncBossDefeated(bossprefab,shardid)
end

---
---UNKNOWN
---
---@param world_id idk 
---@param tags idk 
---@param world_data idk 
---所在文件: scripts/shardnetworking.lua
function Shard_OnShardConnected(world_id,tags,world_data)
end

---
---UNKNOWN
---
---@param selection idk 
---@param user_id idk 
---所在文件: scripts/shardnetworking.lua
function Shard_ReceiveVote(selection,user_id)
end

---
---UNKNOWN
---
---@param exists idk 
---@param shardid idk 
---所在文件: scripts/shardnetworking.lua
function Shard_SyncMermKingPauldron(exists,shardid)
end

---
---UNKNOWN
---
---所在文件: scripts/shardnetworking.lua
function Shard_IsMaster()
end

---
---UNKNOWN
---
---@param exists idk 
---@param shardid idk 
---所在文件: scripts/shardnetworking.lua
function Shard_SyncMermKingExists(exists,shardid)
end

---
---UNKNOWN
---
---@param world_id idk 
---所在文件: scripts/shardnetworking.lua
function Shard_IsWorldFull(world_id)
end

---
---UNKNOWN
---
---@param world_id idk 
---所在文件: scripts/shardnetworking.lua
function Shard_IsWorldAvailable(world_id)
end

---
---UNKNOWN
---
---@param session_id idk 
---所在文件: scripts/shardnetworking.lua
function Shard_UpdateMasterSessionId(session_id)
end

---
---UNKNOWN
---
---@param exists idk 
---@param shardid idk 
---所在文件: scripts/shardnetworking.lua
function Shard_SyncMermKingCrown(exists,shardid)
end

---
---UNKNOWN
---
---所在文件: scripts/shardnetworking.lua
function Shard_WorldSave()
end

---
---UNKNOWN
---
---@param user_id idk 
---所在文件: scripts/shardnetworking.lua
function Shard_OnDiceRollRequest(user_id)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/shardnetworking.lua
function Shard_UpdatePortalState(inst)
end

---
---UNKNOWN
---
---@param world_id idk 
---@param is_resync idk 
---所在文件: scripts/shardnetworking.lua
function Shard_SyncWorldSettings(world_id,is_resync)
end

---
---UNKNOWN
---
---@param exists idk 
---@param shardid idk 
---所在文件: scripts/shardnetworking.lua
function Shard_SyncMermKingTrident(exists,shardid)
end

---
---UNKNOWN
---
---@param world_id idk 
---@param state idk 
---@param tags idk 
---@param world_data idk 
---所在文件: scripts/shardnetworking.lua
function Shard_UpdateWorldState(world_id,state,tags,world_data)
end

---
---UNKNOWN
---
---所在文件: scripts/shardnetworking.lua
function Shard_StopVote()
end

---
---UNKNOWN
---
---@param command_id idk 
---@param starter_id idk 
---@param target_id idk 
---所在文件: scripts/shardnetworking.lua
function Shard_StartVote(command_id,starter_id,target_id)
end

---
---UNKNOWN
---
---所在文件: scripts/shardnetworking.lua
function Shard_GetConnectedShards()
end

---
---UNKNOWN
---
---@param cb idk 
---@param files idk 
---所在文件: scripts/fileutil.lua
function EraseFiles(cb,files)
end

---
---UNKNOWN
---
---@param cb idk 
---@param files idk 
---所在文件: scripts/fileutil.lua
function CheckFiles(cb,files)
end

---
---UNKNOWN
---
---@param full_skins_list idk 
---@param filters idk 
---所在文件: scripts/skinsfiltersutils.lua
function ApplyFilters(full_skins_list,filters)
end

---
---UNKNOWN
---
---@param inst idk 
---@param anim idk 
---@param animloop idk 
---@param pushanim idk 
---@param animduration idk 
---@param endanim idk 
---@param endanimloop idk 
---@param soundevent idk 
---@param soundname idk 
---@param soundduration idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntablePlayAnim(inst,anim,animloop,pushanim,animduration,endanim,endanimloop,soundevent,soundname,soundduration,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeGhostPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function RemoveFromRegrowthManager(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---所在文件: scripts/standardcomponents.lua
function MakeHugeFreezableCharacter(inst,sym,offset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---所在文件: scripts/standardcomponents.lua
function MakeMediumFreezableCharacter(inst,sym,offset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallFreezableCharacter(inst,sym,offset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param fn idk 
---@param secondrxn idk 
---@param ignoreinitialresult idk 
---@param ignoresecondaryresult idk 
---所在文件: scripts/standardcomponents.lua
function AddHauntableCustomReaction(inst,fn,secondrxn,ignoreinitialresult,ignoresecondaryresult)
end

---
---UNKNOWN
---
---@param inst idk 
---@param x idk 
---@param z idk 
---所在文件: scripts/standardcomponents.lua
function ToggleOnAllObjectCollisionsAt(inst,x,z)
end

---
---UNKNOWN
---
---@param inst idk 
---@param size idk 
---@param offset idk 
---@param scale idk 
---@param swap_bank idk 
---@param float_index idk 
---@param swap_data idk 
---所在文件: scripts/standardcomponents.lua
function MakeInventoryFloatable(inst,size,offset,scale,swap_bank,float_index,swap_data)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function ToggleOnCharacterCollisions(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---所在文件: scripts/standardcomponents.lua
function MakeTinyFreezableCharacter(inst,sym,offset)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function DefaultBurntCorpseFn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeTinyGhostPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param time idk 
---@param offset idk 
---@param structure idk 
---@param sym idk 
---所在文件: scripts/standardcomponents.lua
function MakeMediumBurnable(inst,time,offset,structure,sym)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeTinyFlyingCharacterPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function ChangeToCharacterPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function ChangeToWaterObstaclePhysics(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launch_chance idk 
---@param smash_chance idk 
---@param speed idk 
---@param cooldown idk 
---@param launch_haunt_value idk 
---@param smash_haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableLaunchAndSmash(inst,launch_chance,smash_chance,speed,cooldown,launch_haunt_value,smash_haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeDeployableFertilizer(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeLargePropagator(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function AddToRegrowthManager(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---所在文件: scripts/standardcomponents.lua
function MakeMediumBurnableCharacter(inst,sym,offset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param rad idk 
---@param height idk 
---所在文件: scripts/standardcomponents.lua
function MakeHeavyObstaclePhysics(inst,rad,height)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launchchance idk 
---@param perishchance idk 
---@param speed idk 
---@param perishpct idk 
---@param cooldown idk 
---@param launch_haunt_value idk 
---@param perish_haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableLaunchAndPerish(inst,launchchance,perishchance,speed,perishpct,cooldown,launch_haunt_value,perish_haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function DefaultExtinguishFn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableIgnite(inst,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeDeployableFertilizerPristine(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeFlyingGiantCharacterPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param time idk 
---@param sym idk 
---@param offset idk 
---@param scale idk 
---所在文件: scripts/standardcomponents.lua
function MakeMediumBurnableCorpse(inst,time,sym,offset,scale)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function ChangeToGhostPhysics(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launchchance idk 
---@param prefabchance idk 
---@param speed idk 
---@param cooldown idk 
---@param newprefab idk 
---@param prefab_haunt_value idk 
---@param launch_haunt_value idk 
---@param nofx idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableLaunchOrChangePrefab(inst,launchchance,prefabchance,speed,cooldown,newprefab,prefab_haunt_value,launch_haunt_value,nofx)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launchchance idk 
---@param ignitechance idk 
---@param speed idk 
---@param cooldown idk 
---@param launch_haunt_value idk 
---@param ignite_haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableLaunchAndIgnite(inst,launchchance,ignitechance,speed,cooldown,launch_haunt_value,ignite_haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param ripple idk 
---@param shadow idk 
---所在文件: scripts/standardcomponents.lua
function AddDefaultRippleSymbols(inst,ripple,shadow)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function DefaultBurntFn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function ChangeToInventoryItemPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param attacker idk 
---@param tag idk 
---所在文件: scripts/standardcomponents.lua
function PreventTargetingOnAttacked(inst,attacker,tag)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function DefaultIgniteFn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeWaxablePlant(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param material idk 
---@param onbroken idk 
---@param onrepaired idk 
---所在文件: scripts/standardcomponents.lua
function MakeForgeRepairable(inst,material,onbroken,onrepaired)
end

---
---UNKNOWN
---
---@param inst idk 
---@param rad idk 
---@param height idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallHeavyObstaclePhysics(inst,rad,height)
end

---
---UNKNOWN
---
---@param inst idk 
---@param panictime idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntablePanic(inst,panictime,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function ToggleOffAllObjectCollisions(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallBurnableCharacter(inst,sym,offset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param rad idk 
---@param height idk 
---@param restitution idk 
---所在文件: scripts/standardcomponents.lua
function MakeWaterObstaclePhysics(inst,rad,height,restitution)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function DefaultExtinguishCorpseFn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function DefaultBurnFn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeMediumPropagator(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeSnowCovered(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function AddHauntableDropItemOrWork(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallPerishableCreaturePristine(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function PreventCharacterCollisionsWithPlacedObjects(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param rad idk 
---@param height idk 
---所在文件: scripts/standardcomponents.lua
function MakePondPhysics(inst,rad,height)
end

---
---UNKNOWN
---
---@param inst idk 
---@param newprefab idk 
---@param chance idk 
---@param haunt_value idk 
---@param nofx idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableChangePrefab(inst,newprefab,chance,haunt_value,nofx)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---所在文件: scripts/standardcomponents.lua
function MakeLargeFreezableCharacter(inst,sym,offset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param work_chance idk 
---@param ignite_chance idk 
---@param cooldown idk 
---@param work_haunt_value idk 
---@param ignite_haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableWorkAndIgnite(inst,work_chance,ignite_chance,cooldown,work_haunt_value,ignite_haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param state idk 
---@param chancefn idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableGoToStateWithChanceFunction(inst,state,chancefn,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeInventoryPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param state idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableGoToState(inst,state,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param time idk 
---@param offset idk 
---@param structure idk 
---@param sym idk 
---所在文件: scripts/standardcomponents.lua
function MakeLargeBurnable(inst,time,offset,structure,sym)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launchchance idk 
---@param dropchance idk 
---@param speed idk 
---@param cooldown idk 
---@param launch_haunt_value idk 
---@param drop_haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableLaunchAndDropFirstItem(inst,launchchance,dropchance,speed,cooldown,launch_haunt_value,drop_haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeSnowCoveredPristine(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableFreeze(inst,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param time idk 
---@param sym idk 
---@param offset idk 
---@param scale idk 
---所在文件: scripts/standardcomponents.lua
function MakeLargeBurnableCorpse(inst,time,sym,offset,scale)
end

---
---UNKNOWN
---
---@param inst idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableDropFirstItem(inst,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableWork(inst,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param time idk 
---@param offset idk 
---@param structure idk 
---@param sym idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallBurnable(inst,time,offset,structure,sym)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeProjectilePhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param chance idk 
---@param speed idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntableLaunch(inst,chance,speed,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param starvetime idk 
---@param oninventory idk 
---@param ondropped idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallPerishableCreature(inst,starvetime,oninventory,ondropped)
end

---
---UNKNOWN
---
---@param inst idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntable(inst,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---@param starvetime idk 
---@param oninventory idk 
---@param ondropped idk 
---所在文件: scripts/standardcomponents.lua
function MakeFeedableSmallLivestock(inst,starvetime,oninventory,ondropped)
end

---
---UNKNOWN
---
---所在文件: scripts/standardcomponents.lua
function MakeDragonflyBait()
end

---
---UNKNOWN
---
---@param inst idk 
---@param rad idk 
---@param height idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallObstaclePhysics(inst,rad,height)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function ChangeToGiantCharacterPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function ChangeToInventoryPhysics(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function ToggleOffCharacterCollisions(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function DefaultBurntStructureFn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param perishpct idk 
---@param chance idk 
---@param cooldown idk 
---@param haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntablePerish(inst,perishpct,chance,cooldown,haunt_value)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeNoGrowInWinter(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeFeedableSmallLivestockPristine(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param time idk 
---@param sym idk 
---@param offset idk 
---@param scale idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallBurnableCorpse(inst,time,sym,offset,scale)
end

---
---UNKNOWN
---
---@param inst idk 
---@param rad idk 
---@param height idk 
---所在文件: scripts/standardcomponents.lua
function MakeObstaclePhysics(inst,rad,height)
end

---
---UNKNOWN
---
---@param inst idk 
---@param rad idk 
---@param height idk 
---所在文件: scripts/standardcomponents.lua
function ChangeToObstaclePhysics(inst,rad,height)
end

---
---UNKNOWN
---
---@param inst idk 
---@param sym idk 
---@param offset idk 
---@param scale idk 
---所在文件: scripts/standardcomponents.lua
function MakeLargeBurnableCharacter(inst,sym,offset,scale)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function RemovePhysicsColliders(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeGiantCharacterPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeFlyingCharacterPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---@param mass idk 
---@param rad idk 
---所在文件: scripts/standardcomponents.lua
function MakeCharacterPhysics(inst,mass,rad)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/standardcomponents.lua
function MakeSmallPropagator(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param panictime idk 
---@param panicchance idk 
---@param ignitechance idk 
---@param cooldown idk 
---@param panic_haunt_value idk 
---@param ignite_haunt_value idk 
---所在文件: scripts/standardcomponents.lua
function MakeHauntablePanicAndIgnite(inst,panictime,panicchance,ignitechance,cooldown,panic_haunt_value,ignite_haunt_value)
end

---
---UNKNOWN
---
---@param pt idk 
---所在文件: scripts/giantutils.lua
function GetWanderAwayPoint(pt)
end

---
---线性插值
---
---@param a number # 起始值
---@param b number # 结束值
---@param t number # 插值比例，范围通常在 0 到 1 之间
---@return number # 插值后的值
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function Lerp(a,b,t)
end

---
---UNKNOWN
---
---@param num idk 
---@param min idk 
---@param max idk 
---所在文件: scripts/mathutil.lua
function Clamp(num,min,max)
end

---
---映射（Mapping），将一个范围 `[a, b]` 中的值 `i` 映射到另一个范围 `[x, y]`
---
---@param i number 
---@param a number 
---@param b number 
---@param x number 
---@param y number 
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function Remap(i,a,b,x,y)
end

---
---弧度差值计算（Radian Difference Calculation），计算两个弧度值之间的绝对差值，并标准化到 `-π` 到 `π` 范围内
---
---@param rot1 number
---@param rot2 number
---@return number
---@nodiscard 
---所在文件: scripts/mathutil.lua
---
---author: lan
function DiffAngleRad(rot1,rot2)
end

---
---角度差值计算（Angle Difference Calculation），计算两个角度之间的绝对差值，并标准化到 `-180` 到 `180` 度范围
---
---@param rot1 number
---@param rot2 number
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function DiffAngle(rot1,rot2)
end

---
---向上偏置舍入（Biased Rounding Up），将数值四舍五入到指定的小数位
---
---@param num number # 要四舍五入的数值
---@param idp number|nil # 小数位数，默认为 0
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function RoundBiasedUp(num,idp)
end

---
---最接近倍数舍入（Rounding to Nearest Multiple），将数值四舍五入到最接近的倍数
---
---@param numToRound number # 要四舍五入的数值
---@param multiple number # 倍数
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function RoundToNearest(numToRound,multiple)
end

---
---二维平方距离计算（Squared Distance Calculation in XY Plane），计算两个点在 XY 平面上的平方距离
---
---@param p1 {x:number,y:number} 
---@param p2 {x:number,y:number} 
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function DistXYSq(p1,p2)
end

---
---弧度差值计算（Radian Difference Calculation），计算两个弧度值之间的绝对差值，并标准化到 `-π` 到 `π` 范围内
---
---@param rot number # 要标准化的弧度值
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function ReduceAngleRad(rot)
end

---
---向下偏置舍入（Biased Rounding Down），将数值四舍五入到指定的小数位
---
---@param num number # 要四舍五入的数值
---@param idp number|nil # 小数位数，默认为 0
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function RoundBiasedDown(num,idp)
end

---
---根据游戏时间生成一个正弦波。`mod` 参数用于调整波的周期，`abs` 参数决定是否取绝对值
---
---@param mod number|nil # 周期调整因子，默认为 `1`
---@param abs boolean|nil # 决定是否取绝对值
---@param inst ent|nil # 实例对象，用于获取实体生存时间
---@return number # 返回生成的正弦波值
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function GetSineVal(mod,abs,inst)
end

---
---角度标准化（Angle Normalization），将角度值标准化到 `-180` 到 `180` 度范围内
---
---@param rot number # 要标准化的角度值
---@return number
---@nodiscard 
---所在文件: scripts/mathutil.lua
---
---author: lan
function ReduceAngle(rot)
end

---
---二维平方距离计算（Squared Distance Calculation in XZ Plane），计算两个点在 XZ 平面上的平方距离
---
---@param p1 {x:number,y:number}  
---@param p2 {x:number,y:number}  
---@return number
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function DistXZSq(p1,p2)
end

---
---偶数检测（Even Number Detection），判断数值是否为偶数
---
---@param num number # 要判断的数值
---@return boolean
---@nodiscard
---所在文件: scripts/mathutil.lua
---
---author: lan
function IsNumberEven(num)
end

---
---UNKNOWN
---
---@param tag idk 
---@param inst idk 
---@param radius idk 
---所在文件: scripts/simutil.lua
function GetClosestInstWithTag(tag,inst,radius)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param range idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindPlayersInRange(x,y,z,range,isalive)
end

---
---UNKNOWN
---
---@param tag idk 
---@param inst idk 
---@param radius idk 
---所在文件: scripts/simutil.lua
function GetRandomInstWithTag(tag,inst,radius)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindClosestPlayer(x,y,z,isalive)
end

---
---UNKNOWN
---
---@param mode idk 
---@param duration idk 
---@param speed idk 
---@param scale idk 
---@param platform idk 
---所在文件: scripts/simutil.lua
function ShakeAllCamerasOnPlatform(mode,duration,speed,scale,platform)
end

---
---UNKNOWN
---
---@param mode idk 
---@param duration idk 
---@param speed idk 
---@param scale idk 
---@param source_or_pt idk 
---@param maxDist idk 
---所在文件: scripts/simutil.lua
function ShakeAllCameras(mode,duration,speed,scale,source_or_pt,maxDist)
end

---
---UNKNOWN
---
---@param inst idk 
---@param radius idk 
---@param ignoreheight idk 
---@param musttags idk 
---@param canttags idk 
---@param mustoneoftags idk 
---@param fn idk 
---所在文件: scripts/simutil.lua
function FindClosestEntity(inst,radius,ignoreheight,musttags,canttags,mustoneoftags,fn)
end

---
---UNKNOWN
---
---@param inst idk 
---@param range idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindClosestPlayerToInstOnLand(inst,range,isalive)
end

---
---UNKNOWN
---
---@param inst idk 
---@param target idk 
---所在文件: scripts/simutil.lua
function CanEntitySeeTarget(inst,target)
end

---
---UNKNOWN
---
---@param inst idk 
---@param x idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/simutil.lua
function CanEntitySeePoint(inst,x,y,z)
end

---
---UNKNOWN
---
---@param owner idk 
---@param radius idk 
---@param furthestfirst idk 
---@param positionoverride idk 
---@param ignorethese idk 
---@param onlytheseprefabs idk 
---@param allowpickables idk 
---@param worker idk 
---@param extra_filter idk 
---所在文件: scripts/simutil.lua
function FindPickupableItem(owner,radius,furthestfirst,positionoverride,ignorethese,onlytheseprefabs,allowpickables,worker,extra_filter)
end

---
---UNKNOWN
---
---@param imagename idk 
---所在文件: scripts/simutil.lua
function GetMinimapAtlas_Internal(imagename)
end

---
---UNKNOWN
---
---@param amount idk 
---@param forced idk 
---所在文件: scripts/simutil.lua
function SpringGrowthMod(amount,forced)
end

---
---获取`xxx.tex`所在的图集路径
---
---@param imagename string 
---@param no_fallback boolean|nil # 这个不填,则不会返回nil,如果你xml找不到,这里也会返回一个错误的路径, 官方为了省一点性能才这样写的 <br> 如果你是MOD物品, 建议一律填`true`, 这样如果没找到, 会返回nil <br> 
---所在文件: scripts/simutil.lua
function GetInventoryItemAtlas(imagename,no_fallback)
end

---
---UNKNOWN
---
---所在文件: scripts/simutil.lua
function GetWorld()
end

---
---UNKNOWN
---
---@param position idk 
---@param range idk 
---所在文件: scripts/simutil.lua
function FindNearbyLand(position,range)
end

---
---UNKNOWN
---
---@param position idk 
---@param start_angle idk 
---@param radius idk 
---@param attempts idk 
---@param check_los idk 
---@param ignore_walls idk 
---@param customcheckfn idk 
---@param allow_water idk 
---@param allow_boats idk 
---所在文件: scripts/simutil.lua
function FindWalkableOffset(position,start_angle,radius,attempts,check_los,ignore_walls,customcheckfn,allow_water,allow_boats)
end

---
---UNKNOWN
---
---@param imagename idk 
---所在文件: scripts/simutil.lua
function GetSkilltreeIconAtlas(imagename)
end

---
---UNKNOWN
---
---@param imagename idk 
---所在文件: scripts/simutil.lua
function GetSkilltreeIconAtlas_Internal(imagename)
end

---
---UNKNOWN
---
---@param imagename idk 
---所在文件: scripts/simutil.lua
function GetSkilltreeBG(imagename)
end

---
---UNKNOWN
---
---@param atlas idk 
---@param imagename idk 
---所在文件: scripts/simutil.lua
function RegisterSkilltreeBGAtlas(atlas,imagename)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/simutil.lua
function CanEntitySeeInStorm(inst)
end

---
---UNKNOWN
---
---@param imagename idk 
---所在文件: scripts/simutil.lua
function GetSkilltreeBG_Internal(imagename)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param range idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindClosestPlayerInRange(x,y,z,range,isalive)
end

---
---UNKNOWN
---
---@param amount idk 
---@param forced idk 
---所在文件: scripts/simutil.lua
function SpringCombatMod(amount,forced)
end

---
---UNKNOWN
---
---@param imagename idk 
---所在文件: scripts/simutil.lua
function GetScrapbookIconAtlas_Internal(imagename)
end

---
---UNKNOWN
---
---@param imagename idk 
---所在文件: scripts/simutil.lua
function GetMinimapAtlas(imagename)
end

---
---UNKNOWN
---
---@param imagename idk 
---@param no_fallback idk 
---所在文件: scripts/simutil.lua
function GetInventoryItemAtlas_Internal(imagename,no_fallback)
end

---
---UNKNOWN
---
---@param event idk 
---所在文件: scripts/simutil.lua
function ApplyExtraEvent(event)
end

---
---UNKNOWN
---
---@param tag idk 
---@param inst idk 
---@param radius idk 
---所在文件: scripts/simutil.lua
function DeleteCloseEntsWithTag(tag,inst,radius)
end

---
---UNKNOWN
---
---@param event idk 
---所在文件: scripts/simutil.lua
function ApplySpecialEvent(event)
end

---
---UNKNOWN
---
---@param inst idk 
---@param erode_time idk 
---@param cb idk 
---@param restore idk 
---所在文件: scripts/simutil.lua
function ErodeCB(inst,erode_time,cb,restore)
end

---
---UNKNOWN
---
---所在文件: scripts/simutil.lua
function CalledFrom()
end

---
---获取图鉴图集
---
---@param imagename string # 要带`.tex`后缀
---@return string # 图集路径
---@nodiscard 
---所在文件: scripts/simutil.lua
function GetScrapbookIconAtlas(imagename)
end

---
---UNKNOWN
---
---@param inst idk 
---@param rangesq idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function IsAnyOtherPlayerNearInst(inst,rangesq,isalive)
end

---
---UNKNOWN
---
---@param obj idk 
---@param time idk 
---所在文件: scripts/simutil.lua
function TemporarilyRemovePhysics(obj,time)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/simutil.lua
function FindSafeSpawnLocation(x,y,z)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param range idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function IsAnyPlayerInRange(x,y,z,range,isalive)
end

---
---UNKNOWN
---
---@param inst idk 
---@param erode_time idk 
---所在文件: scripts/simutil.lua
function ErodeAway(inst,erode_time)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/simutil.lua
function CanEntitySeeInDark(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/simutil.lua
function FindCharlieRezSpotFor(inst)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param rangesq idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindClosestPlayerOnLandInRangeSq(x,y,z,rangesq,isalive)
end

---
---UNKNOWN
---
---@param inst idk 
---@param radius idk 
---@param fn idk 
---@param musttags idk 
---@param canttags idk 
---@param mustoneoftags idk 
---所在文件: scripts/simutil.lua
function FindEntity(inst,radius,fn,musttags,canttags,mustoneoftags)
end

---
---UNKNOWN
---
---@param inst idk 
---@param range idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindClosestPlayerToInst(inst,range,isalive)
end

---
---UNKNOWN
---
---@param position idk 
---@param start_angle idk 
---@param radius idk 
---@param attempts idk 
---@param check_los idk 
---@param ignore_walls idk 
---@param customcheckfn idk 
---@param allow_boats idk 
---所在文件: scripts/simutil.lua
function FindSwimmableOffset(position,start_angle,radius,attempts,check_los,ignore_walls,customcheckfn,allow_boats)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param rangesq idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindClosestPlayerInRangeSq(x,y,z,rangesq,isalive)
end

---
---UNKNOWN
---
---@param start_angle idk 
---@param radius idk 
---@param attempts idk 
---@param test_fn idk 
---所在文件: scripts/simutil.lua
function FindValidPositionByFan(start_angle,radius,attempts,test_fn)
end

---
---UNKNOWN
---
---@param item idk 
---@param total_time idk 
---@param start_scale idk 
---@param end_scale idk 
---所在文件: scripts/simutil.lua
function AnimateUIScale(item,total_time,start_scale,end_scale)
end

---
---UNKNOWN
---
---@param position idk 
---@param range idk 
---所在文件: scripts/simutil.lua
function FindNearbyOcean(position,range)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param rangesq idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function IsAnyPlayerInRangeSq(x,y,z,rangesq,isalive)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param rangesq idk 
---@param isalive idk 
---所在文件: scripts/simutil.lua
function FindPlayersInRangeSq(x,y,z,rangesq,isalive)
end

---
---UNKNOWN
---
---所在文件: scripts/simutil.lua
function GetPlayer()
end

---
---UNKNOWN
---
---@param achievement idk 
---@param data idk 
---所在文件: scripts/gamelogic.lua
function RecordEventAchievementSharedProgress(achievement,data)
end

---
---UNKNOWN
---
---所在文件: scripts/gamelogic.lua
function DeactivateWorld()
end

---
---UNKNOWN
---
---所在文件: scripts/gamelogic.lua
function ShowLoading()
end

---
---UNKNOWN
---
---所在文件: scripts/gamelogic.lua
function HideCancelTip()
end

---
---UNKNOWN
---
---所在文件: scripts/gamelogic.lua
function ForceAuthenticationDialog()
end

---
---UNKNOWN
---
---所在文件: scripts/gamelogic.lua
function ShowDemoExpiredDialog()
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/gamelogic.lua
function SetGlobalErrorWidget(...)
end

---
---UNKNOWN
---
---@param achievement idk 
---@param src idk 
---@param data idk 
---所在文件: scripts/gamelogic.lua
function RecordEventAchievementProgress(achievement,src,data)
end

---
---UNKNOWN
---
---所在文件: scripts/gamelogic.lua
function GetTimePlaying()
end

---
---UNKNOWN
---
---@param achievement idk 
---@param data idk 
---所在文件: scripts/gamelogic.lua
function RecordEventAchievementProgressForAllPlayers(achievement,data)
end

---
---UNKNOWN
---
---所在文件: scripts/gamelogic.lua
function ShowCancelTip()
end

---
---UNKNOWN
---
---@param tasksetname idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddTaskSetPreInit(tasksetname,fn)
end

---
---UNKNOWN
---
---@param tile_id idk 
---@param target_tile_id idk 
---@param moveafter idk 
---所在文件: scripts/modutil.lua
function ChangeMiniMapTileRenderOrder(tile_id,target_tile_id,moveafter)
end

---
---添加SG的事件监听器API通用函数片段
---
---@param stategraph idk 
---@param event idk 
---所在文件: scripts/modutil.lua
function AddStategraphEvent(stategraph,event)
end

---
---UNKNOWN
---
---@param tile_id idk 
---@param target_tile_id idk 
---@param moveafter idk 
---所在文件: scripts/modutil.lua
function ChangeTileRenderOrder(tile_id,target_tile_id,moveafter)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/modutil.lua
function ModInfoname(name)
end

---
---UNKNOWN
---
---@param arg1 idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function AddTask(arg1,...)
end

---
---UNKNOWN
---
---@param stringtable idk 
---@param id idk 
---@param tipstring idk 
---@param controltipdata idk 
---所在文件: scripts/modutil.lua
function AddLoadingTip(stringtable,id,tipstring,controltipdata)
end

---
---注册服务器分片rpc
---
---@param namespace idk 
---@param name idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddShardModRPCHandler(namespace,name,fn)
end

---
---UNKNOWN
---
---@param tile_id idk 
---@param propertyname idk 
---@param value idk 
---所在文件: scripts/modutil.lua
function SetTileProperty(tile_id,propertyname,value)
end

---
---UNKNOWN
---
---@param message idk 
---@param level idk 
---所在文件: scripts/modutil.lua
function moderror(message,level)
end

---
---添加SG的状态API通用函数片段
---
---@param stategraph idk 
---@param state idk 
---所在文件: scripts/modutil.lua
function AddStategraphState(stategraph,state)
end

---
---UNKNOWN
---
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddModShadersInit(fn)
end

---
---UNKNOWN
---
---@param falloff_id idk 
---@param propertyname idk 
---@param value idk 
---所在文件: scripts/modutil.lua
function SetFalloffProperty(falloff_id,propertyname,value)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/modutil.lua
function Recipe(...)
end

---
---UNKNOWN
---
---@param taskname idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddTaskPreInit(taskname,fn)
end

---
---注册客户端rpc,接收服务器的数据用的
---
---@param namespace string 命名空间(写mod名即可)
---@param name string 当前命名空间下的rpc名字
---@param fn fun(...: any) # 客机的玩家为 ThePlayer
---所在文件: scripts/modutil.lua
function AddClientModRPCHandler(namespace,name,fn)
end

---
---UNKNOWN
---
---@param arg1 idk 
---@param arg2 idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function AddLevel(arg1,arg2,...)
end

---
---UNKNOWN
---
---所在文件: scripts/modutil.lua
function ReloadPreloadAssets()
end

---
---添加修改世界配置API通用函数片段
---
---@param fn fun()
---所在文件: scripts/modutil.lua
function AddLevelPreInitAny(fn)
end

---
---添加修改类API通用函数片段
---
---@param package string 
---@param fn fun(self: class, ...: any)
---所在文件: scripts/modutil.lua
function AddClassPostConstruct(package,fn)
end

---
---UNKNOWN
---
---@param recipe_name idk 
---@param filter_name idk 
---所在文件: scripts/modutil.lua
function AddRecipeToFilter(recipe_name,filter_name)
end

---
---UNKNOWN
---
---@param falloff_id idk 
---@param falloff_id_id idk 
---@param moveafter idk 
---所在文件: scripts/modutil.lua
function ChangeFalloffRenderOrder(falloff_id,falloff_id_id,moveafter)
end

---
---UNKNOWN
---
---@param namespace idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function GetModRPCHandler(namespace,name)
end

---
---UNKNOWN
---
---@param falloff_id idk 
---@param falloff_def idk 
---所在文件: scripts/modutil.lua
function AddFalloffTexture(falloff_id,falloff_def)
end

---
---UNKNOWN
---
---@param description idk 
---所在文件: scripts/modutil.lua
function GetCustomizeDescription(description)
end

---
---UNKNOWN
---
---@param arg1 idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function AddTaskSet(arg1,...)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/modutil.lua
function RemoveRemapSoundEvent(name)
end

---
---只有第一次世界启动时执行
---
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddGamePostInit(fn)
end

---
---UNKNOWN
---
---@param id idk 
---所在文件: scripts/modutil.lua
function AddPopup(id)
end

---
---添加动作API
---
---@param id string 动作id
---@param str string 动作译名
---@param fn fun(act: table)
---所在文件: scripts/modutil.lua
function AddAction(id,str,fn)
end

---
---UNKNOWN
---
---@param atlas idk 
---@param tex idk 
---所在文件: scripts/modutil.lua
function RegisterScrapbookIconAtlas(atlas,tex)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/modutil.lua
function AddModReleaseID(name)
end

---
---添加分解配方API,供分解法杖拆解
---
---@param name idk 
---@param return_ingredients idk 
---所在文件: scripts/modutil.lua
function AddDeconstructRecipe(name,return_ingredients)
end

---
---修改脑子API
---
---@param brain idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddBrainPostInit(brain,fn)
end

---
---UNKNOWN
---
---@param namespace idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function GetShardModRPCHandler(namespace,name)
end

---
---UNKNOWN
---
---@param roomname idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddRoomPreInit(roomname,fn)
end

---
---获取当前mod配置项的值
---
---@param optionname string 配置项
---@param get_local_config any 是否获取本地设置,一般不填
---@return any value
---所在文件: scripts/modutil.lua
---@nodiscard
function GetModConfigData(optionname,get_local_config)
end

---
---添加修改修改状态图(SG)API通用函数片段,初始化时最后执行
---
---@param stategraph idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddStategraphPostInit(stategraph,fn)
end

---
---UNKNOWN
---
---@param category idk 
---@param categoryatlas idk 
---@param categoryicon idk 
---所在文件: scripts/modutil.lua
function SetLoadingTipCategoryIcon(category,categoryatlas,categoryicon)
end

---
---UNKNOWN
---
---@param weighttable idk 
---@param weightdata idk 
---所在文件: scripts/modutil.lua
function SetLoadingTipCategoryWeights(weighttable,weightdata)
end

---
---UNKNOWN
---
---@param namespace idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function GetShardModRPC(namespace,name)
end

---
---UNKNOWN
---
---@param atlas idk 
---@param tex idk 
---所在文件: scripts/modutil.lua
function RegisterSkilltreeIconsAtlas(atlas,tex)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/modutil.lua
function modprint(...)
end

---
---UNKNOWN
---
---@param atlas idk 
---@param imagename idk 
---所在文件: scripts/modutil.lua
function RegisterInventoryItemAtlas(atlas,imagename)
end

---
---UNKNOWN
---
---@param atlas idk 
---@param charactername idk 
---所在文件: scripts/modutil.lua
function RegisterSkilltreeBGForCharacter(atlas,charactername)
end

---
---UNKNOWN
---
---所在文件: scripts/modutil.lua
function ReloadFrontEndAssets()
end

---
---UNKNOWN
---
---@param namespace idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function GetClientModRPCHandler(namespace,name)
end

---
---UNKNOWN
---
---@param name idk 
---@param symbol idk 
---所在文件: scripts/modutil.lua
function ExcludeClothingSymbolForModCharacter(name,symbol)
end

---
---UNKNOWN
---
---@param command_name idk 
---@param init_options_fn idk 
---@param process_result_fn idk 
---@param vote_timeout idk 
---所在文件: scripts/modutil.lua
function AddVoteCommand(command_name,init_options_fn,process_result_fn,vote_timeout)
end

---
---UNKNOWN
---
---@param command_name idk 
---@param data idk 
---所在文件: scripts/modutil.lua
function AddUserCommand(command_name,data)
end

---
---UNKNOWN
---
---@param levelid idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddLevelPreInit(levelid,fn)
end

---
---UNKNOWN
---
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddModShadersSortAndEnable(fn)
end

---
---UNKNOWN
---
---@param stringtable idk 
---@param id idk 
---所在文件: scripts/modutil.lua
function RemoveLoadingTip(stringtable,id)
end

---
---UNKNOWN
---
---@param namespace idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function GetClientModRPC(namespace,name)
end

---
---UNKNOWN
---
---@param namespace idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function GetModRPC(namespace,name)
end

---
---发送服务器其他世界RPC调用
---
---@param id_table idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function SendModRPCToShard(id_table,...)
end

---
---修改玩家
---
---@param fn fun(inst: ent):...
---所在文件: scripts/modutil.lua
function AddPlayerPostInit(fn)
end

---
---UNKNOWN
---
---@param category idk 
---@param group idk 
---@param name idk 
---@param itemsettings idk 
---所在文件: scripts/modutil.lua
function AddCustomizeItem(category,group,name,itemsettings)
end

---
---UNKNOWN
---
---@param arg1 idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function AddRoom(arg1,...)
end

---
---服务器向客户端发RPC
---
---@param id_table idk MOD_RPC[命名空间][rpcID]
---@param user_id userid # 玩家userid, 注意userid在线和离线不一样
---@param ... any 要传到客户端的数据,不可以是表,可不填
---所在文件: scripts/modutil.lua
function SendModRPCToClient(id_table,user_id,...)
end

---
---UNKNOWN
---
---@param tile_id idk 
---@param propertyname idk 
---@param value idk 
---所在文件: scripts/modutil.lua
function SetMiniMapTileProperty(tile_id,propertyname,value)
end

---
---向服务器发RPC
---
---@param id_table idk MOD_RPC[命名空间][rpcID]
---@param ... any 要传到服务器的数据,不可以是表,可不填
---所在文件: scripts/modutil.lua
function SendModRPCToServer(id_table,...)
end

---
---注册服务器ModRPC
---
---@param namespace string 命名空间(写mod名即可)
---@param name string 当前命名空间下的rpc名字
---@param fn fun(player: ent,...: any)
---所在文件: scripts/modutil.lua
function AddModRPCHandler(namespace,name,fn)
end

---
---UNKNOWN
---
---@param package idk 
---@param classname idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddGlobalClassPostConstruct(package,classname,fn)
end

---
---修改已有的组件的初始化API
---
---@param component componentID 组件名
---@param fn fun(self)
---所在文件: scripts/modutil.lua
function AddComponentPostInit(component,fn)
end

---
---UNKNOWN
---
---@param tile_name idk 
---@param tile_range idk 
---@param tile_data idk 
---@param ground_tile_def idk 
---@param minimap_tile_def idk 
---@param turf_def idk 
---所在文件: scripts/modutil.lua
function AddTile(tile_name,tile_range,tile_data,ground_tile_def,minimap_tile_def,turf_def)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/modutil.lua
function AddReplicableComponent(name)
end

---
---添加自定义原型机API通用函数片段,is_crafting_station在附近才能制作
---
---@param prototyper_prefab idk 
---@param data idk 
---所在文件: scripts/modutil.lua
function AddPrototyperDef(prototyper_prefab,data)
end

---
---UNKNOWN
---
---@param name idk 
---@param ingredients idk 
---@param tech idk 
---@param config idk 
---@param filters idk 
---所在文件: scripts/modutil.lua
function AddRecipe2(name,ingredients,tech,config,filters)
end

---
---UNKNOWN
---
---@param fn fun(inst: ent):...
---所在文件: scripts/modutil.lua
function AddPrefabPostInitAny(fn)
end

---
---UNKNOWN
---
---@param arg1 idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function AddLocation(arg1,...)
end

---
---修改已有的预制物的初始化API
---
---@param prefab PrefabID 
---@param fn fun(inst: ent):...
---所在文件: scripts/modutil.lua
function AddPrefabPostInit(prefab,fn)
end

---
---UNKNOWN
---
---@param name idk 
---@param new_name idk 
---所在文件: scripts/modutil.lua
function RemapSoundEvent(name,new_name)
end

---
---UNKNOWN
---
---@param path idk 
---@param lang idk 
---所在文件: scripts/modutil.lua
function LoadPOFile(path,lang)
end

---
---UNKNOWN
---
---@param rec_str idk 
---@param rec_sort idk 
---@param rec_atlas idk 
---@param rec_icon idk 
---@param rec_owner_tag idk 
---@param rec_crafting_station idk 
---所在文件: scripts/modutil.lua
function AddRecipeTab(rec_str,rec_sort,rec_atlas,rec_icon,rec_owner_tag,rec_crafting_station)
end

---
---UNKNOWN
---
---@param arg1 idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function AddRecipe(arg1,...)
end

---
---UNKNOWN
---
---@param arg1 idk 
---@param ... idk 
---所在文件: scripts/modutil.lua
function AddStartLocation(arg1,...)
end

---
---UNKNOWN
---
---@param name idk 
---@param ingredients idk 
---@param tech idk 
---@param config idk 
---@param extra_filters idk 
---所在文件: scripts/modutil.lua
function AddCharacterRecipe(name,ingredients,tech,config,extra_filters)
end

---
---添加小地图图标
---
---@param atlaspath string 路径
---所在文件: scripts/modutil.lua
function AddMinimapAtlas(atlaspath)
end

---
---UNKNOWN
---
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddTaskSetPreInitAny(fn)
end

---
---UNKNOWN
---
---@param names idk 
---@param tags idk 
---@param cancook idk 
---@param candry idk 
---所在文件: scripts/modutil.lua
function AddIngredientValues(names,tags,cancook,candry)
end

---
---UNKNOWN
---
---@param filter_def idk 
---@param index idk 
---所在文件: scripts/modutil.lua
function AddRecipeFilter(filter_def,index)
end

---
---添加Mod角色API
---
---@param name idk 
---@param gender idk 
---@param modes idk 
---所在文件: scripts/modutil.lua
function AddModCharacter(name,gender,modes)
end

---
---UNKNOWN
---
---@param category idk 
---@param name idk 
---@param text idk 
---@param desc idk 
---@param atlas idk 
---@param order idk 
---所在文件: scripts/modutil.lua
function AddCustomizeGroup(category,name,text,desc,atlas,order)
end

---
---UNKNOWN
---
---@param cooker idk 
---@param recipe idk 
---所在文件: scripts/modutil.lua
function AddCookerRecipe(cooker,recipe)
end

---
---UNKNOWN
---
---@param recipe_name idk 
---@param filter_name idk 
---所在文件: scripts/modutil.lua
function RemoveRecipeFromFilter(recipe_name,filter_name)
end

---
---修改配方API
---
---@param recipename idk 
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddRecipePostInit(recipename,fn)
end

---
---UNKNOWN
---
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddRecipePostInitAny(fn)
end

---
---UNKNOWN
---
---@param focusid idk 
---@param hasfocus idk 
---所在文件: scripts/modutil.lua
function SetModHUDFocus(focusid,hasfocus)
end

---
---添加SG的动作触发器API通用函数片段
---
---@param stategraph idk 
---@param handler idk 
---所在文件: scripts/modutil.lua
function AddStategraphActionHandler(stategraph,handler)
end

---
---UNKNOWN
---
---@param range_name idk 
---@param range_start idk 
---@param range_end idk 
---所在文件: scripts/modutil.lua
function RegisterTileRange(range_name,range_start,range_end)
end

---
---每次重载世界时都会执行
---
---@param fn idk 
---所在文件: scripts/modutil.lua
function AddSimPostInit(fn)
end

---
---UNKNOWN
---
---@param game_mode idk 
---@param game_mode_text idk 
---所在文件: scripts/modutil.lua
function AddGameMode(game_mode,game_mode_text)
end

---
---UNKNOWN
---
---@param category idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function RemoveCustomizeItem(category,name)
end

---
---UNKNOWN
---
---@param category idk 
---@param name idk 
---所在文件: scripts/modutil.lua
function RemoveCustomizeGroup(category,name)
end

---
---UNKNOWN
---
---@param test idk 
---@param message idk 
---所在文件: scripts/modutil.lua
function modassert(test,message)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/entityreplica.lua
function AddReplicableComponent(name)
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function GetFEVisibleCharacterList()
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport.lua
function IsDLCEnabled(index)
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport.lua
function IsDLCInstalled(index)
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function EnableAllMenuDLC()
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function DisableAllDLC()
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function EnableAllDLC()
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport.lua
function DisableDLC(index)
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport.lua
function RegisterDLC(index)
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function RegisterAllDLC()
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport.lua
function EnableExclusiveDLC(index)
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function InitAllDLC()
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport.lua
function EnableDLC(index)
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport.lua
function InitDLC(index)
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function GetActiveCharacterList()
end

---
---UNKNOWN
---
---所在文件: scripts/dlcsupport.lua
function GetSelectableCharacterList()
end

---
---组件绑定动作
---
---@param actiontype string 动作类型
---@param component string 组件
---@param fn fun(inst: ent,doer: ent,pos: Vector3,target: ent,actions: table,right: boolean)
---@param modname idk 
---所在文件: scripts/componentactions.lua
function AddComponentAction(actiontype,component,fn,modname)
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsConsole()
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsLinux()
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsWin32()
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsXB1()
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsPS4()
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsRail()
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsSteamDeck()
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/main.lua
function loadfile(filename)
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsSteam()
end

---
---UNKNOWN
---
---所在文件: scripts/main.lua
function IsNotConsole()
end

---
---UNKNOWN
---
---@param base_dta idk 
---@param tbl_dta idk 
---@param lkp_var idk 
---@param file idk 
---所在文件: scripts/createstringspo.lua
function PrintTranslatedStringTableV1(base_dta,tbl_dta,lkp_var,file)
end

---
---UNKNOWN
---
---@param filename idk 
---@param root idk 
---@param tbl_dta idk 
---@param tbl_lkp idk 
---所在文件: scripts/createstringspo.lua
function CreateStringsPOTv1(filename,root,tbl_dta,tbl_lkp)
end

---
---UNKNOWN
---
---@param filename idk 
---@param root idk 
---@param tbl_dta idk 
---@param tbl_lkp idk 
---所在文件: scripts/createstringspo.lua
function CreateStringsPOTv2(filename,root,tbl_dta,tbl_lkp)
end

---
---UNKNOWN
---
---所在文件: scripts/debugprint.lua
function GetConsoleOutputList()
end

---
---UNKNOWN
---
---@param s idk 
---所在文件: scripts/debugprint.lua
function escape_lua_pattern(s)
end

---
---UNKNOWN
---
---@param fn idk 
---所在文件: scripts/debugprint.lua
function AddPrintLogger(fn)
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetFirstActiveSpecialEvent()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetActiveFestivalEventServerName()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetFestivalEventInfo()
end

---
---UNKNOWN
---
---@param event idk 
---所在文件: scripts/constants.lua
function IsFestivalEventActive(event)
end

---
---UNKNOWN
---
---@param event idk 
---所在文件: scripts/constants.lua
function IsPreviousFestivalEvent(event)
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function Server_IsTournamentActive()
end

---
---UNKNOWN
---
---@param special_event idk 
---@param extra_events idk 
---所在文件: scripts/constants.lua
function GetAllActiveEvents(special_event,extra_events)
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function IsAnySpecialEventActive()
end

---
---UNKNOWN
---
---@param festival idk 
---所在文件: scripts/constants.lua
function GetFestivalEventSeasons(festival)
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetFestivalEventSkinTag()
end

---
---UNKNOWN
---
---@param r idk 
---@param g idk 
---@param b idk 
---所在文件: scripts/constants.lua
function RGB(r,g,b)
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetActiveSpecialEventCount()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetActiveFestivalEventStatsFilePrefix()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetActiveFestivalEventAchievementStrings()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function IsAny_YearOfThe_EventActive()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function Client_IsTournamentActive()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetActiveFestivalProductName()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetWorldTileMap()
end

---
---UNKNOWN
---
---@param event idk 
---所在文件: scripts/constants.lua
function IsSpecialEventActive(event)
end

---
---UNKNOWN
---
---@param festival idk 
---@param season idk 
---所在文件: scripts/constants.lua
function GetFestivalEventServerName(festival,season)
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function IsAnyFestivalEventActive()
end

---
---UNKNOWN
---
---所在文件: scripts/constants.lua
function GetSpecialEventSkinTag()
end

---
---UNKNOWN
---
---@param fn idk 
---所在文件: scripts/dlcsupport_strings.lua
function MakeAllSuffixes(fn)
end

---
---UNKNOWN
---
---@param inst idk 
---@param name idk 
---@param adjective idk 
---所在文件: scripts/dlcsupport_strings.lua
function ConstructAdjectivedName(inst,name,adjective)
end

---
---UNKNOWN
---
---@param item idk 
---@param usePrefix idk 
---所在文件: scripts/dlcsupport_strings.lua
function SetUsesPrefix(item,usePrefix)
end

---
---UNKNOWN
---
---@param fn idk 
---所在文件: scripts/dlcsupport_strings.lua
function MakeAllPrefixes(fn)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/worldtiledefs.lua
function GroundImage(name)
end

---
---UNKNOWN
---
---@param tile idk 
---所在文件: scripts/worldtiledefs.lua
function GetTileInfo(tile)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/worldtiledefs.lua
function GroundAtlas(name)
end

---
---UNKNOWN
---
---@param tile idk 
---所在文件: scripts/worldtiledefs.lua
function LookupTileInfo(tile)
end

---
---UNKNOWN
---
---@param inst idk 
---@param volume idk 
---@param ispredicted idk 
---所在文件: scripts/worldtiledefs.lua
function PlayFootstep(inst,volume,ispredicted)
end

---
---UNKNOWN
---
---@param obj idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/vector3.lua
function ToVector3(obj,y,z)
end

---
---UNKNOWN
---
---@param theta idk 
---@param radius idk 
---所在文件: scripts/vector3.lua
function Vector3FromTheta(theta,radius)
end

---
---UNKNOWN
---
---@param tuning idk 
---所在文件: scripts/worldsettings_overrides.lua
function OverrideTuningVariables(tuning)
end

---
---UNKNOWN
---
---@param instance_id idk 
---所在文件: scripts/splitscreenutils_pc.lua
function IsGameInstance(instance_id)
end

---
---UNKNOWN
---
---所在文件: scripts/splitscreenutils_pc.lua
function HaveMultipleViewports()
end

---
---UNKNOWN
---
---所在文件: scripts/splitscreenutils_pc.lua
function IsSplitScreen()
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/componentutil.lua
function DecayCharlieResidueAndGoOnCooldownIfItExists(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param require_health idk 
---所在文件: scripts/componentutil.lua
function IsEntityDeadOrGhost(inst,require_health)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/componentutil.lua
function MakeRoseTarget_CreateFuel_IncreasedHorror(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param require_health idk 
---所在文件: scripts/componentutil.lua
function IsEntityDead(inst,require_health)
end

---
---UNKNOWN
---
---@param dug_ground idk 
---@param x idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/componentutil.lua
function HandleDugGround(dug_ground,x,y,z)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/componentutil.lua
function MakeRoseTarget_CreateFuel(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/componentutil.lua
function GetStackSize(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/componentutil.lua
function DecayCharlieResidueIfItExists(inst)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param r idk 
---所在文件: scripts/componentutil.lua
function FindVirtualOceanEntity(x,y,z,r)
end

---
---UNKNOWN
---
---@param inst idk 
---@param data idk 
---@param maxregentime idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_Pickable_PreLoad(inst,data,maxregentime)
end

---
---UNKNOWN
---
---@param inst idk 
---@param spawnperiod idk 
---@param enabled idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_ChildSpawner_SpawnPeriod(inst,spawnperiod,enabled)
end

---
---UNKNOWN
---
---@param inst idk 
---@param data idk 
---@param spawnperiod_max idk 
---@param regenperiod_max idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_ChildSpawner_PreLoad(inst,data,spawnperiod_max,regenperiod_max)
end

---
---UNKNOWN
---
---@param inst idk 
---@param startdelay idk 
---@param enabled idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_Spawner_SpawnDelay(inst,startdelay,enabled)
end

---
---UNKNOWN
---
---@param inst idk 
---@param regentime idk 
---@param enabled idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_Pickable_RegenTime(inst,regentime,enabled)
end

---
---UNKNOWN
---
---@param inst idk 
---@param data idk 
---@param timername idk 
---@param maxmultiplier idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_Timer_PreLoad_Fix(inst,data,timername,maxmultiplier)
end

---
---UNKNOWN
---
---@param inst idk 
---@param data idk 
---@param maxstartdelay idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_Spawner_PreLoad(inst,data,maxstartdelay)
end

---
---UNKNOWN
---
---@param inst idk 
---@param regenperiod idk 
---@param enabled idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_ChildSpawner_RegenPeriod(inst,regenperiod,enabled)
end

---
---UNKNOWN
---
---@param inst idk 
---@param data idk 
---@param timername idk 
---@param maxtimeleft idk 
---所在文件: scripts/worldsettingsutil.lua
function WorldSettings_Timer_PreLoad(inst,data,timername,maxtimeleft)
end

---
---UNKNOWN
---
---@param o idk 
---所在文件: scripts/json.lua
function isEncodable(o)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode_scanNumber(s,startPos)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode_scanConstant(s,startPos)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode_scanArray(s,startPos)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode(s,startPos)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode_scanComment(s,startPos)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode_scanString(s,startPos)
end

---
---UNKNOWN
---
---@param s idk 
---所在文件: scripts/json.lua
function encodeString_compliant(s)
end

---
---UNKNOWN
---
---@param s idk 
---所在文件: scripts/json.lua
function encodeString(s)
end

---
---UNKNOWN
---
---@param t idk 
---所在文件: scripts/json.lua
function isArray(t)
end

---
---UNKNOWN
---
---@param v idk 
---所在文件: scripts/json.lua
function encode(v)
end

---
---UNKNOWN
---
---@param v idk 
---所在文件: scripts/json.lua
function encode_compliant(v)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode_scanWhitespace(s,startPos)
end

---
---UNKNOWN
---
---@param s idk 
---@param startPos idk 
---所在文件: scripts/json.lua
function decode_scanObject(s,startPos)
end

---
---UNKNOWN
---
---所在文件: scripts/json.lua
function null()
end

---
---UNKNOWN
---
---@param a idk 
---@param b idk 
---@param limit idk 
---所在文件: scripts/stringutil.lua
function DamLevDist(a,b,limit)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/stringutil.lua
function do_search_subwords(...)
end

---
---UNKNOWN
---
---@param action idk 
---@param modifier idk 
---所在文件: scripts/stringutil.lua
function GetActionString(action,modifier)
end

---
---UNKNOWN
---
---@param herocharacter idk 
---所在文件: scripts/stringutil.lua
function GetCharacterDescription(herocharacter)
end

---
---UNKNOWN
---
---@param inst idk 
---@param action idk 
---@param reason idk 
---所在文件: scripts/stringutil.lua
function GetActionFailString(inst,action,reason)
end

---
---UNKNOWN
---
---@param ret idk 
---@param charactertable idk 
---@param inst idk 
---@param item idk 
---@param modifier idk 
---所在文件: scripts/stringutil.lua
function GetDescription_AddSpecialCases(ret,charactertable,inst,item,modifier)
end

---
---UNKNOWN
---
---@param time idk 
---所在文件: scripts/stringutil.lua
function str_play_time(time)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/stringutil.lua
function ProcessString(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param line idk 
---@param modifier idk 
---@param nil_missing idk 
---所在文件: scripts/stringutil.lua
function GetLine(inst,line,modifier,nil_missing)
end

---
---格式化字符串 <br> usage: <br> subfmt("this is my {adjective} string, read it {number} times!", {adjective="cool", number="five"}) <br> => "this is my cool string, read it five times"
---
---@param s string # 待格式化的字符串, 用 `{key}` 作为占位符
---@param dict table # 字典, `key` 为占位符, `value` 为替换的值
---@return string
---@nodiscard
---所在文件: scripts/stringutil.lua
---
---author: lan
function subfmt(s,dict)
    return (s:gsub('(%b{})', function(w) return dict[w:sub(2, -2)] or w end))
end

---
---UNKNOWN
---
---@param os_time idk 
---所在文件: scripts/stringutil.lua
function str_date(os_time)
end

---
---UNKNOWN
---
---@param charactername idk 
---所在文件: scripts/stringutil.lua
function GetGenderStrings(charactername)
end

---
---UNKNOWN
---
---@param character idk 
---@param string idk 
---所在文件: scripts/stringutil.lua
function GetSpecialCharacterPostProcess(character,string)
end

---
---UNKNOWN
---
---@param inst idk 
---@param stringtype idk 
---@param modifier idk 
---@param nil_missing idk 
---所在文件: scripts/stringutil.lua
function GetString(inst,stringtype,modifier,nil_missing)
end

---
---UNKNOWN
---
---@param string idk 
---所在文件: scripts/stringutil.lua
function Umlautify(string)
end

---
---UNKNOWN
---
---所在文件: scripts/stringutil.lua
function CraftMonkeySpeech()
end

---
---UNKNOWN
---
---@param time idk 
---所在文件: scripts/stringutil.lua
function str_seconds(time)
end

---
---UNKNOWN
---
---@param character idk 
---所在文件: scripts/stringutil.lua
function GetSpecialCharacterString(character)
end

---
---UNKNOWN
---
---@param s idk 
---所在文件: scripts/stringutil.lua
function TrimString(s)
end

---
---UNKNOWN
---
---所在文件: scripts/stringutil.lua
function CraftGiberish()
end

---
---UNKNOWN
---
---@param str idk 
---所在文件: scripts/stringutil.lua
function FirstToUpper(str)
end

---
---UNKNOWN
---
---@param inst idk 
---@param item idk 
---@param modifier idk 
---所在文件: scripts/stringutil.lua
function GetDescription(inst,item,modifier)
end

---
---UNKNOWN
---
---@param minimal_load idk 
---所在文件: scripts/klump.lua
function LoadAccessibleKlumpFiles(minimal_load)
end

---
---UNKNOWN
---
---@param klump_file idk 
---@param cipher idk 
---@param suppress_print idk 
---所在文件: scripts/klump.lua
function LoadKlumpFile(klump_file,cipher,suppress_print)
end

---
---UNKNOWN
---
---@param string_id idk 
---@param json_str idk 
---所在文件: scripts/klump.lua
function ApplyKlumpToStringTable(string_id,json_str)
end

---
---UNKNOWN
---
---@param klump_file idk 
---@param cipher idk 
---@param suppress_print idk 
---所在文件: scripts/klump.lua
function LoadKlumpString(klump_file,cipher,suppress_print)
end

---
---UNKNOWN
---
---@param klump_file idk 
---所在文件: scripts/klump.lua
function IsKlumpLoaded(klump_file)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Dist(p1_x,p1_z,p2_x,p2_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_NormalAndLength(p1_x,p1_z)
end

---
---UNKNOWN
---
---@param a_x idk 
---@param a_z idk 
---@param b_x idk 
---@param b_z idk 
---@param theta idk 
---所在文件: scripts/vecutil.lua
function VecUtil_RotateAroundPoint(a_x,a_z,b_x,b_z,theta)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_GetAngleInRads(p1_x,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_z idk 
---@param percent idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Slerp(p1_x,p1_z,p2_x,p2_z,percent)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Add(p1_x,p1_z,p2_x,p2_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_z idk 
---@param percent idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Lerp(p1_x,p1_z,p2_x,p2_z,percent)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_DistSq(p1_x,p1_z,p2_x,p2_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Dot(p1_x,p1_z,p2_x,p2_z)
end

---
---UNKNOWN
---
---@param dir_x idk 
---@param dir_z idk 
---@param theta idk 
---所在文件: scripts/vecutil.lua
function VecUtil_RotateDir(dir_x,dir_z,theta)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_GetAngleInDegrees(p1_x,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Normalize(p1_x,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_NormalizeNoNaN(p1_x,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Length(p1_x,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_LengthSq(p1_x,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param scale idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Scale(p1_x,p1_z,scale)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_z idk 
---所在文件: scripts/vecutil.lua
function VecUtil_Sub(p1_x,p1_z,p2_x,p2_z)
end

---
---UNKNOWN
---
---@param action idk 
---所在文件: scripts/profiler.lua
function _profiler_hook_wrapper_by_call(action)
end

---
---UNKNOWN
---
---@param action idk 
---所在文件: scripts/profiler.lua
function _profiler_hook_wrapper_by_time(action)
end

---
---UNKNOWN
---
---@param variant idk 
---@param sampledelay idk 
---所在文件: scripts/profiler.lua
function newProfiler(variant,sampledelay)
end

---
---UNKNOWN
---
---@param item idk 
---@param chunk idk 
---所在文件: scripts/stats.lua
function ProfileStatsAddItemChunk(item,chunk)
end

---
---UNKNOWN
---
---@param item idk 
---@param value idk 
---所在文件: scripts/stats.lua
function SuUsedAdd(item,value)
end

---
---UNKNOWN
---
---@param item idk 
---@param value idk 
---所在文件: scripts/stats.lua
function ProfileStatsAdd(item,value)
end

---
---UNKNOWN
---
---@param item idk 
---@param value idk 
---所在文件: scripts/stats.lua
function SuUsed(item,value)
end

---
---UNKNOWN
---
---所在文件: scripts/stats.lua
function WasSuUsed()
end

---
---UNKNOWN
---
---@param value idk 
---所在文件: scripts/stats.lua
function SetSuper(value)
end

---
---UNKNOWN
---
---@param field idk 
---@param value idk 
---所在文件: scripts/stats.lua
function ProfileStatsAppendToField(field,value)
end

---
---UNKNOWN
---
---@param field idk 
---@param value idk 
---所在文件: scripts/stats.lua
function ProfileStatsSetField(field,value)
end

---
---UNKNOWN
---
---@param field idk 
---@param value idk 
---所在文件: scripts/stats.lua
function ProfileStatsAddToField(field,value)
end

---
---UNKNOWN
---
---所在文件: scripts/stats.lua
function GetClientMetricsData()
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/stats.lua
function ProfileStatsGet(item)
end

---
---UNKNOWN
---
---@param item idk 
---@param value idk 
---所在文件: scripts/stats.lua
function ProfileStatsSet(item,value)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/usercommands.lua
function RailUserCommandRemove(name)
end

---
---通过玩家userid获取玩家
---
---@param input string # userid, 字符串, 只有player有 
---@return ent|nil # 玩家
---@nodiscard
---所在文件: scripts/usercommands.lua
function UserToPlayer(input)
end

---
---UNKNOWN
---
---@param input idk 
---所在文件: scripts/usercommands.lua
function UserToClient(input)
end

---
---UNKNOWN
---
---所在文件: scripts/usercommands.lua
function GetEmotesWordPredictionDictionary()
end

---
---UNKNOWN
---
---@param input idk 
---所在文件: scripts/usercommands.lua
function UserToClientID(input)
end

---
---UNKNOWN
---
---@param input idk 
---所在文件: scripts/usercommands.lua
function UserToName(input)
end

---
---UNKNOWN
---
---所在文件: scripts/usercommands.lua
function HandleUserCmdQueue()
end

---
---UNKNOWN
---
---@param mod idk 
---@param name idk 
---@param data idk 
---所在文件: scripts/usercommands.lua
function AddModUserCommand(mod,name,data)
end

---
---UNKNOWN
---
---@param name idk 
---@param displayname idk 
---@param displayparams idk 
---@param extra_alias idk 
---所在文件: scripts/usercommands.lua
function RailUserCommandInject(name,displayname,displayparams,extra_alias)
end

---
---UNKNOWN
---
---@param command idk 
---@param property idk 
---@param default idk 
---所在文件: scripts/usercommands.lua
function ResolveCommandStringProperty(command,property,default)
end

---
---UNKNOWN
---
---@param text idk 
---所在文件: scripts/input.lua
function OnInputText(text)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---所在文件: scripts/input.lua
function OnMouseMove(x,y)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---所在文件: scripts/input.lua
function OnPosition(x,y)
end

---
---UNKNOWN
---
---@param control idk 
---@param digitalvalue idk 
---@param analogvalue idk 
---所在文件: scripts/input.lua
function OnControl(control,digitalvalue,analogvalue)
end

---
---UNKNOWN
---
---@param gesture idk 
---所在文件: scripts/input.lua
function OnGesture(gesture)
end

---
---UNKNOWN
---
---所在文件: scripts/input.lua
function OnFloatingTextInputDismissed()
end

---
---UNKNOWN
---
---@param key idk 
---@param is_up idk 
---所在文件: scripts/input.lua
function OnInputKey(key,is_up)
end

---
---UNKNOWN
---
---@param button idk 
---@param is_up idk 
---@param x idk 
---@param y idk 
---所在文件: scripts/input.lua
function OnMouseButton(button,is_up,x,y)
end

---
---UNKNOWN
---
---@param deviceId idk 
---@param controlId idk 
---@param inputId idk 
---@param hasChanged idk 
---所在文件: scripts/input.lua
function OnControlMapped(deviceId,controlId,inputId,hasChanged)
end

---
---UNKNOWN
---
---@param dt idk 
---@param ignore_player idk 
---所在文件: scripts/update.lua
function LongUpdate(dt,ignore_player)
end

---
---UNKNOWN
---
---@param classname idk 
---@param fn idk 
---所在文件: scripts/update.lua
function RegisterStaticComponentUpdate(classname,fn)
end

---
---UNKNOWN
---
---@param classname idk 
---@param fn idk 
---所在文件: scripts/update.lua
function RegisterStaticComponentLongUpdate(classname,fn)
end

---
---UNKNOWN
---
---@param dt idk 
---所在文件: scripts/update.lua
function PostUpdate(dt)
end

---
---UNKNOWN
---
---@param dt idk 
---所在文件: scripts/update.lua
function Update(dt)
end

---
---UNKNOWN
---
---@param dt idk 
---所在文件: scripts/update.lua
function StaticUpdate(dt)
end

---
---UNKNOWN
---
---@param dt idk 
---所在文件: scripts/update.lua
function PostPhysicsWallUpdate(dt)
end

---
---UNKNOWN
---
---@param dt idk 
---所在文件: scripts/update.lua
function WallUpdate(dt)
end

---
---UNKNOWN
---
---@param actioncode idk 
---@param mod_name idk 
---所在文件: scripts/actions.lua
function SetClientRequestedAction(actioncode,mod_name)
end

---
---UNKNOWN
---
---所在文件: scripts/actions.lua
function ClearClientRequestedAction()
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/strict.lua
function global(...)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Length(p1_x,p1_y,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_y idk 
---@param p2_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Sub(p1_x,p1_y,p1_z,p2_x,p2_y,p2_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_y idk 
---@param p2_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_DistSq(p1_x,p1_y,p1_z,p2_x,p2_y,p2_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_NormalAndLength(p1_x,p1_y,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_y idk 
---@param p2_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Add(p1_x,p1_y,p1_z,p2_x,p2_y,p2_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_y idk 
---@param p2_z idk 
---@param percent idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Lerp(p1_x,p1_y,p1_z,p2_x,p2_y,p2_z,percent)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_y idk 
---@param p2_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Dot(p1_x,p1_y,p1_z,p2_x,p2_y,p2_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Normalize(p1_x,p1_y,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---@param scale idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Scale(p1_x,p1_y,p1_z,scale)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_LengthSq(p1_x,p1_y,p1_z)
end

---
---UNKNOWN
---
---@param p1_x idk 
---@param p1_y idk 
---@param p1_z idk 
---@param p2_x idk 
---@param p2_y idk 
---@param p2_z idk 
---所在文件: scripts/vec3util.lua
function Vec3Util_Dist(p1_x,p1_y,p1_z,p2_x,p2_y,p2_z)
end

---
---UNKNOWN
---
---@param time idk 
---@param sound_event idk 
---所在文件: scripts/stategraph.lua
function SoundTimeEvent(time,sound_event)
end

---
---UNKNOWN
---
---@param frame idk 
---@param fn idk 
---所在文件: scripts/stategraph.lua
function FrameEvent(frame,fn)
end

---
---UNKNOWN
---
---@param frame idk 
---@param sound_event idk 
---所在文件: scripts/stategraph.lua
function SoundFrameEvent(frame,sound_event)
end

---
---UNKNOWN
---
---@param foods idk 
---所在文件: scripts/spicedfoods.lua
function GenerateSpicedFoods(foods)
end

---
---UNKNOWN
---
---@param density idk 
---所在文件: scripts/regrowthutil.lua
function CalculateFiveRadius(density)
end

---
---UNKNOWN
---
---@param x idk 
---@param z idk 
---@param prefab idk 
---所在文件: scripts/regrowthutil.lua
function GetFiveRadius(x,z,prefab)
end

---
---UNKNOWN
---
---@param eventname idk 
---@param path idk 
---所在文件: scripts/entityscript.lua
function event_server_data(eventname,path)
end

---
---UNKNOWN
---
---@param pack_key idk 
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function DoesPackHaveItem(pack_key,item_key)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function IsDailyGiftItemPending()
end

---
---UNKNOWN
---
---@param user_profile idk 
---所在文件: scripts/skinsutils.lua
function CacheCurrentVanityItems(user_profile)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function IsPackRestrictedDueToOwnership(item_type)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsItemInAnyPack(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function IsItemMarketable(item)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function GetMysteryBoxItemID(item_type)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function HasNewSkinDLCEntitlements()
end

---
---UNKNOWN
---
---@param pack_key idk 
---所在文件: scripts/skinsutils.lua
function DoesPackHaveACharacter(pack_key)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPortraitNameForItem(item_key)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetOwnedItemCounts()
end

---
---UNKNOWN
---
---@param prefab idk 
---所在文件: scripts/skinsutils.lua
function IsRestrictedCharacter(prefab)
end

---
---UNKNOWN
---
---@param hero idk 
---所在文件: scripts/skinsutils.lua
function GetCharacterSkinBases(hero)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function GetItemCollectionName(item_type)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function IsUserCommerceBuyRestrictedDueToOwnership(item_type)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPurchaseDisplayForItem(item_key)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsPackGiftable(item_key)
end

---
---UNKNOWN
---
---@param do_sort idk 
---所在文件: scripts/skinsutils.lua
function GetInventorySkinsList(do_sort)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetSkinName(item)
end

---
---UNKNOWN
---
---@param iap_def idk 
---@param total_value idk 
---@param sale_active idk 
---所在文件: scripts/skinsutils.lua
function GetPackSavings(iap_def,total_value,sale_active)
end

---
---UNKNOWN
---
---@param bonus_item idk 
---@param item_counts idk 
---所在文件: scripts/skinsutils.lua
function _BonusItemRewarded(bonus_item,item_counts)
end

---
---UNKNOWN
---
---@param context idk 
---@param list_widget idk 
---@param data idk 
---@param data_index idk 
---所在文件: scripts/skinsutils.lua
function UpdateSkinGrid(context,list_widget,data,data_index)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsPackClothingOnly(item_key)
end

---
---UNKNOWN
---
---@param rarity idk 
---所在文件: scripts/skinsutils.lua
function GetNextRarity(rarity)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function IsItemInCollection(item_type)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetNullFilter()
end

---
---UNKNOWN
---
---@param pack_key idk 
---所在文件: scripts/skinsutils.lua
function DoesPackHaveBelongings(pack_key)
end

---
---UNKNOWN
---
---@param item_key_a idk 
---@param item_key_b idk 
---所在文件: scripts/skinsutils.lua
function CompareReleaseGroup(item_key_a,item_key_b)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/skinsutils.lua
function IsClothingItem(name)
end

---
---UNKNOWN
---
---@param rarity idk 
---所在文件: scripts/skinsutils.lua
function GetFrameSymbolForRarity(rarity)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function OwnsSkinPack(item_key)
end

---
---UNKNOWN
---
---@param prefab idk 
---所在文件: scripts/skinsutils.lua
function IsPrefabSkinned(prefab)
end

---
---UNKNOWN
---
---@param num_item_types idk 
---所在文件: scripts/skinsutils.lua
function GetBoxPopupLayoutDetails(num_item_types)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsPurchasePackCurrency(item_key)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/skinsutils.lua
function IsValidBeefaloClothing(name)
end

---
---UNKNOWN
---
---@param tag idk 
---所在文件: scripts/skinsutils.lua
function GetTypeFromTag(tag)
end

---
---UNKNOWN
---
---@param pack_key idk 
---@param character idk 
---所在文件: scripts/skinsutils.lua
function DoesPackHaveSkinsForCharacter(pack_key,character)
end

---
---UNKNOWN
---
---@param item_key_a idk 
---@param item_key_b idk 
---所在文件: scripts/skinsutils.lua
function CompareItemDataForSortByName(item_key_a,item_key_b)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function WillUnravelBreakEnsemble(item_type)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetBoxBuildForItem(item_key)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsDefaultClothing(item_key)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetAllGameplayItems()
end

---
---UNKNOWN
---
---@param entitlement idk 
---所在文件: scripts/skinsutils.lua
function SetSkinDLCEntitlementReceived(entitlement)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function IsUserCommerceSellAllowedOnItem(item_type)
end

---
---UNKNOWN
---
---@param item_key_a idk 
---@param item_key_b idk 
---所在文件: scripts/skinsutils.lua
function CompareRarities(item_key_a,item_key_b)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPackTotalItems(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetRarityForItem(item)
end

---
---UNKNOWN
---
---@param prefab idk 
---所在文件: scripts/skinsutils.lua
function IsCharacterOwned(prefab)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function IsItemIsReward(item_type)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsDefaultSkin(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---@param tag idk 
---所在文件: scripts/skinsutils.lua
function DoesItemHaveTag(item,tag)
end

---
---UNKNOWN
---
---@param currentcharacter idk 
---@param preview_skins idk 
---@param filter idk 
---所在文件: scripts/skinsutils.lua
function ValidatePreviewItems(currentcharacter,preview_skins,filter)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsPackFeatured(item_key)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPackCollection(item_key)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPurchasePackCurrencyOutput(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetTypeForItem(item)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetLockedSkinFilter()
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/skinsutils.lua
function IsGameplayItem(name)
end

---
---UNKNOWN
---
---@param user_profile idk 
---所在文件: scripts/skinsutils.lua
function IsShopNew(user_profile)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function WillUnravelBreakRestrictedCharacter(item_type)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPlayerPortraitAtlasAndTex(item_key)
end

---
---UNKNOWN
---
---@param active_cosmetics idk 
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function GetRemotePlayerVanityItem(active_cosmetics,item_type)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetDailyGiftItem()
end

---
---UNKNOWN
---
---@param entitlement idk 
---所在文件: scripts/skinsutils.lua
function AddNewSkinDLCEntitlement(entitlement)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPackTotalSets(item_key)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetFeaturedPacks()
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetWeaveableSkinFilter()
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsDefaultBeefClothing(item_key)
end

---
---UNKNOWN
---
---@param iap_def idk 
---@param sale_active idk 
---所在文件: scripts/skinsutils.lua
function GetPriceFromIAPDef(iap_def,sale_active)
end

---
---UNKNOWN
---
---@param rarity idk 
---所在文件: scripts/skinsutils.lua
function IsHeirloomRarity(rarity)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/skinsutils.lua
function GetBuildForItem(name)
end

---
---UNKNOWN
---
---@param item_key_a idk 
---@param item_key_b idk 
---@param item_counts idk 
---所在文件: scripts/skinsutils.lua
function CompareItemDataForSortByCount(item_key_a,item_key_b,item_counts)
end

---
---UNKNOWN
---
---@param prefab idk 
---@param cur_skin idk 
---所在文件: scripts/skinsutils.lua
function GetPrevOwnedSkin(prefab,cur_skin)
end

---
---UNKNOWN
---
---@param herocharacter idk 
---所在文件: scripts/skinsutils.lua
function GetAffinityFilterForHero(herocharacter)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetNewSkinDLCEntitlement()
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetLoaderAtlasAndTex(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetEventIconForItem(item)
end

---
---UNKNOWN
---
---@param user_profile idk 
---所在文件: scripts/skinsutils.lua
function IsAnyItemNew(user_profile)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetBigPortraitAnimForItem(item_key)
end

---
---UNKNOWN
---
---@param item_key_a idk 
---@param item_key_b idk 
---所在文件: scripts/skinsutils.lua
function CompareItemDataForSortByRarity(item_key_a,item_key_b)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetReleaseGroup(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetSkinData(item)
end

---
---UNKNOWN
---
---@param user_profile idk 
---所在文件: scripts/skinsutils.lua
function ValidateItemsInProfile(user_profile)
end

---
---UNKNOWN
---
---@param item_type idk 
---@param popup_txt idk 
---所在文件: scripts/skinsutils.lua
function GetSkinUsableOnString(item_type,popup_txt)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/skinsutils.lua
function IsValidClothing(name)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPackGiftDLCID(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetModifiedRarityStringForItem(item)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function ShouldDisplayItemInCollection(item_type)
end

---
---UNKNOWN
---
---@param iap_def idk 
---所在文件: scripts/skinsutils.lua
function IsSaleActive(iap_def)
end

---
---UNKNOWN
---
---@param list idk 
---所在文件: scripts/skinsutils.lua
function CopySkinsList(list)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsPackABundle(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetRarityModifierForItem(item)
end

---
---UNKNOWN
---
---@param user_profile idk 
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function BuildListOfSelectedItems(user_profile,item_type)
end

---
---UNKNOWN
---
---@param herocharacter idk 
---所在文件: scripts/skinsutils.lua
function GetSkinCollectionCompletionForHero(herocharacter)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/skinsutils.lua
function GetSkinModeFromBuild(player)
end

---
---UNKNOWN
---
---@param character idk 
---@param ghost idk 
---@param state_1 idk 
---@param state_2 idk 
---@param state_3 idk 
---所在文件: scripts/skinsutils.lua
function GetPlayerBadgeData(character,ghost,state_1,state_2,state_3)
end

---
---UNKNOWN
---
---@param character idk 
---所在文件: scripts/skinsutils.lua
function GetSkinModes(character)
end

---
---UNKNOWN
---
---@param pack_a idk 
---@param pack_b idk 
---所在文件: scripts/skinsutils.lua
function _IsPackInsideOther(pack_a,pack_b)
end

---
---UNKNOWN
---
---@param screen idk 
---所在文件: scripts/skinsutils.lua
function DisplayInventoryFailedPopup(screen)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPurchasePackOutputItems(item_key)
end

---
---UNKNOWN
---
---@param character idk 
---@param skins_subscreener idk 
---所在文件: scripts/skinsutils.lua
function DisplayCharacterUnownedPopup(character,skins_subscreener)
end

---
---UNKNOWN
---
---@param _cb idk 
---所在文件: scripts/skinsutils.lua
function MakeSkinDLCPopup(_cb)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsUserCommerceAllowedOnItemType(item_key)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function _GetSubPacks(item_key)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function SetDailyGiftItem(item_type)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetProfileFlairAtlasAndTex(item_key)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function IsUserCommerceBuyRestrictedDueType(item_type)
end

---
---UNKNOWN
---
---@param context idk 
---@param parent idk 
---@param scroll_list idk 
---所在文件: scripts/skinsutils.lua
function SkinGridListConstructor(context,parent,scroll_list)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetTotalMysteryBoxCount()
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetMysteryBoxCounts()
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsDefaultMisc(item_key)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function CalculateShopHash()
end

---
---UNKNOWN
---
---@param currentcharacter idk 
---@param selected_skins idk 
---所在文件: scripts/skinsutils.lua
function ValidateItemsLocal(currentcharacter,selected_skins)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsDefaultCharacterSkin(item_key)
end

---
---UNKNOWN
---
---@param user_profile idk 
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function GetMostRecentlySelectedItem(user_profile,item_type)
end

---
---UNKNOWN
---
---@param prefab idk 
---@param cur_skin idk 
---所在文件: scripts/skinsutils.lua
function GetNextOwnedSkin(prefab,cur_skin)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetColorForItem(item)
end

---
---UNKNOWN
---
---@param data idk 
---所在文件: scripts/skinsutils.lua
function GetSkinsDataFromClientTableData(data)
end

---
---UNKNOWN
---
---@param herocharacter idk 
---所在文件: scripts/skinsutils.lua
function HasHeirloomItem(herocharacter)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetFirstOwnedItemId(item_key)
end

---
---UNKNOWN
---
---@param value idk 
---@param iap_def idk 
---@param sale_active idk 
---所在文件: scripts/skinsutils.lua
function BuildPriceStr(value,iap_def,sale_active)
end

---
---UNKNOWN
---
---所在文件: scripts/skinsutils.lua
function GetInventoryTimestamp()
end

---
---UNKNOWN
---
---@param item_key_a idk 
---@param item_key_b idk 
---所在文件: scripts/skinsutils.lua
function CompareItemDataForSortByRelease(item_key_a,item_key_b)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsDefaultSkinOwned(item_key)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function IsUserCommerceBuyAllowedOnItem(item_type)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetSkinDescription(item)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function GetPurchasePackDisplayItems(item_key)
end

---
---UNKNOWN
---
---@param item idk 
---所在文件: scripts/skinsutils.lua
function GetSkinInvIconName(item)
end

---
---UNKNOWN
---
---@param item_type idk 
---所在文件: scripts/skinsutils.lua
function GetCharacterRequiredForItem(item_type)
end

---
---UNKNOWN
---
---@param entitlement idk 
---所在文件: scripts/skinsutils.lua
function IsSkinDLCEntitlementReceived(entitlement)
end

---
---UNKNOWN
---
---@param item_key idk 
---所在文件: scripts/skinsutils.lua
function IsPackBelongingsOnly(item_key)
end

---
---UNKNOWN
---
---@param item_data idk 
---所在文件: scripts/skinsutils.lua
function IsUserCommerceAllowedOnItemData(item_data)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/skinsutils.lua
function IsItemId(name)
end

---
---UNKNOWN
---
---@param recipe_name idk 
---所在文件: scripts/skinstradeutils.lua
function GetBasicFilters(recipe_name)
end

---
---UNKNOWN
---
---@param selections idk 
---所在文件: scripts/skinstradeutils.lua
function GetNumberSelectedItems(selections)
end

---
---UNKNOWN
---
---@param recipe_data idk 
---@param selected_items idk 
---所在文件: scripts/skinstradeutils.lua
function GetSpecialFilters(recipe_data,selected_items)
end

---
---UNKNOWN
---
---@param selections idk 
---所在文件: scripts/skinstradeutils.lua
function GetBasicRecipeMatch(selections)
end

---
---UNKNOWN
---
---@param condition idk 
---@param key idk 
---所在文件: scripts/knownerrors.lua
function known_assert(condition,key)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function minisign_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function cookpot_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function bernie_big_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function ocean_trawler_kit_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function boat_grass_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lightningrod_top_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function winch_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function firepit_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function hutch_fishbowl_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function bernie_big_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function portablecookpot_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function cookpot_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_stone_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function bernie_active_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function chester_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function chester_eyebone_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function steeringwheel_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_glomling_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function abigail_flower_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function chester_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mushroom_farm_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function minisign_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function yellowstaff_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_lamb_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function firesuppressor_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function bundle_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function bugnet_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function bugnet_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function boat_grass_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function bushhat_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param anim_bank idk 
---所在文件: scripts/prefabskin.lua
function minisign_item_init_fn(inst,build_name,anim_bank)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lamp_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function lureplant_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function trophyscale_oversizedveggies_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function dock_woodposts_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function ocean_trawler_kit_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function bushhat_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function portablecookpot_item_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function minisign_drawn_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function resurrectionstatue_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function anchor_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_moonrock_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function gemsocket_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function telebase_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lightningrod_top_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function chester_eyebone_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function ocean_trawler_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param def_build idk 
---所在文件: scripts/prefabskin.lua
function basic_clear_fn(inst,def_build)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function lureplant_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function fence_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_perdling_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function boat_grass_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_kitten_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function fence_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param def_build idk 
---@param filter_fn idk 
---所在文件: scripts/prefabskin.lua
function basic_init_fn(inst,build_name,def_build,filter_fn)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function portablecookpot_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function dock_woodposts_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function researchlab4_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function boat_grass_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function walkingplank_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function heatrock_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function glommerflower_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lamp_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function lureplantbulb_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_stone_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_ruins_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function lureplantbulb_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param fxoffset idk 
---所在文件: scripts/prefabskin.lua
function campfire_init_fn(inst,build_name,fxoffset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function winch_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function researchlab4_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function resurrectionstatue_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lightningrod_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function abigail_flower_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_puppy_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function bundlewrap_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_lamb_builder_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_wood_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function hutch_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function fence_gate_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function trophyscale_fish_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param anim_bank idk 
---所在文件: scripts/prefabskin.lua
function dock_woodposts_item_init_fn(inst,build_name,anim_bank)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function reviver_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function dock_woodposts_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function orangestaff_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function firesuppressor_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function boat_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function premiumwateringcan_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lamp_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function abigail_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function icebox_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_puppy_builder_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param opentop idk 
---所在文件: scripts/prefabskin.lua
function molehat_init_fn(inst,build_name,opentop)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function lantern_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param anim_bank idk 
---所在文件: scripts/prefabskin.lua
function minisign_init_fn(inst,build_name,anim_bank)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function gemsocket_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function steeringwheel_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function bundle_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function molehat_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function icebox_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function reviver_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function coldfirepit_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_dragonling_builder_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function walkingplank_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_wood_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_ruins_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_ruins_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function fence_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_kitten_builder_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function orangestaff_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function portablecookpot_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_malbatross_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function heatrock_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function walkingplank_grass_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function yellowstaff_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function bernie_active_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function glasscutter_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param overridesymbols idk 
---@param followoffset idk 
---所在文件: scripts/prefabskin.lua
function lantern_init_fn(inst,build_name,overridesymbols,followoffset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function nightsword_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_stone_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function cane_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function glasscutter_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function bernie_inactive_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function bernie_inactive_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_malbatross_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_malbatross_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_malbatross_item_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function ocean_trawler_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function telebase_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function mushroom_farm_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function anchor_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mast_item_clear_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function fence_gate_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function fence_gate_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function fence_gate_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function trophyscale_fish_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function fence_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function boat_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function nightsword_clear_fn(inst)
end

---
---UNKNOWN
---
---@param name idk 
---@param info idk 
---所在文件: scripts/prefabskin.lua
function CreatePrefabSkin(name,info)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_moonrock_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function cane_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_stone_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_ruins_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function critter_builder_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_dragonling_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function hutch_fishbowl_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function premiumwateringcan_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lightningrod_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function trophyscale_oversizedveggies_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lightningrod_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_glomling_builder_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lamp_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function abigail_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function anchor_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function hutch_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function steeringwheel_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_moonrock_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function researchlab2_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function boat_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param default_build idk 
---所在文件: scripts/prefabskin.lua
function glomling_init_fn(inst,build_name,default_build)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function boat_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function mastupgrade_lightningrod_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function walkingplank_grass_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function wall_wood_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param anim_bank idk 
---所在文件: scripts/prefabskin.lua
function minisign_drawn_init_fn(inst,build_name,anim_bank)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function critter_perdling_builder_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param default_build idk 
---@param hungry_sound idk 
---所在文件: scripts/prefabskin.lua
function perdling_init_fn(inst,build_name,default_build,hungry_sound)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param default_build idk 
---所在文件: scripts/prefabskin.lua
function pet_init_fn(inst,build_name,default_build)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function steeringwheel_item_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function researchlab2_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param fxoffset idk 
---所在文件: scripts/prefabskin.lua
function coldfirepit_init_fn(inst,build_name,fxoffset)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function campfire_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_wood_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---@param fxoffset idk 
---所在文件: scripts/prefabskin.lua
function firepit_init_fn(inst,build_name,fxoffset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function bundlewrap_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function anchor_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/prefabskin.lua
function glommerflower_clear_fn(inst)
end

---
---UNKNOWN
---
---@param inst idk 
---@param build_name idk 
---所在文件: scripts/prefabskin.lua
function wall_moonrock_item_init_fn(inst,build_name)
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildMoonPulseShader()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildBloomShader()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildColourCubeShader()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildDistortShader()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildModShaders()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function SortAndEnableShaders()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildMoonPulseGradingShader()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildZoomBlurShader()
end

---
---UNKNOWN
---
---所在文件: scripts/postprocesseffects.lua
function BuildLunacyShader()
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetGameModeMaxPlayers(game_mode)
end

---
---UNKNOWN
---
---所在文件: scripts/gamemodes.lua
function GetResetTime()
end

---
---UNKNOWN
---
---所在文件: scripts/gamemodes.lua
function GetGhostSanityDrain()
end

---
---UNKNOWN
---
---所在文件: scripts/gamemodes.lua
function GetIsSpawnModeFixed()
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetGameModeTag(game_mode)
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetIsModGameMode(game_mode)
end

---
---UNKNOWN
---
---@param enabled_mods idk 
---所在文件: scripts/gamemodes.lua
function GetGameModesSpinnerData(enabled_mods)
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetFarmTillSpacing(game_mode)
end

---
---UNKNOWN
---
---@param game_mode idk 
---@param recipe_name idk 
---所在文件: scripts/gamemodes.lua
function IsRecipeValidInGameMode(game_mode,recipe_name)
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetLevelType(game_mode)
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetGameModeString(game_mode)
end

---
---UNKNOWN
---
---@param property idk 
---所在文件: scripts/gamemodes.lua
function GetGameModeProperty(property)
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetMaxItemSlots(game_mode)
end

---
---UNKNOWN
---
---所在文件: scripts/gamemodes.lua
function GetPortalRez()
end

---
---UNKNOWN
---
---所在文件: scripts/gamemodes.lua
function GetGhostEnabled()
end

---
---UNKNOWN
---
---所在文件: scripts/gamemodes.lua
function GetSpawnMode()
end

---
---UNKNOWN
---
---所在文件: scripts/gamemodes.lua
function GetHasResourceRenewal()
end

---
---UNKNOWN
---
---@param game_mode idk 
---所在文件: scripts/gamemodes.lua
function GetGameModeDescriptionString(game_mode)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launcher idk 
---@param basespeed idk 
---所在文件: scripts/physics.lua
function Launch(inst,launcher,basespeed)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launcher idk 
---@param target idk 
---@param speedmult idk 
---@param startheight idk 
---@param startradius idk 
---@param randomangleoffset idk 
---所在文件: scripts/physics.lua
function LaunchAt(inst,launcher,target,speedmult,startheight,startradius,randomangleoffset)
end

---
---UNKNOWN
---
---@param inst idk 
---@param radius idk 
---@param launch_basespeed idk 
---@param launch_speedmult idk 
---@param launch_startheight idk 
---@param launch_startradius idk 
---所在文件: scripts/physics.lua
function LaunchAndClearArea(inst,radius,launch_basespeed,launch_speedmult,launch_startheight,launch_startradius)
end

---
---UNKNOWN
---
---@param guid1 idk 
---@param guid2 idk 
---@param world_position_on_a_x idk 
---@param world_position_on_a_y idk 
---@param world_position_on_a_z idk 
---@param world_position_on_b_x idk 
---@param world_position_on_b_y idk 
---@param world_position_on_b_z idk 
---@param world_normal_on_b_x idk 
---@param world_normal_on_b_y idk 
---@param world_normal_on_b_z idk 
---@param lifetime_in_frames idk 
---所在文件: scripts/physics.lua
function OnPhysicsCollision(guid1,guid2,world_position_on_a_x,world_position_on_a_y,world_position_on_a_z,world_position_on_b_x,world_position_on_b_y,world_position_on_b_z,world_normal_on_b_x,world_normal_on_b_y,world_normal_on_b_z,lifetime_in_frames)
end

---
---UNKNOWN
---
---@param ent idk 
---@param destroyer idk 
---@param kill_all_creatures idk 
---@param remove_entity_as_fallback idk 
---所在文件: scripts/physics.lua
function DestroyEntity(ent,destroyer,kill_all_creatures,remove_entity_as_fallback)
end

---
---UNKNOWN
---
---@param inst idk 
---@param launcher idk 
---@param basespeed idk 
---@param speedmult idk 
---@param startheight idk 
---@param startradius idk 
---@param vertical_speed idk 
---@param force_angle idk 
---所在文件: scripts/physics.lua
function Launch2(inst,launcher,basespeed,speedmult,startheight,startradius,vertical_speed,force_angle)
end

---
---UNKNOWN
---
---@param base idk 
---@param _ctor idk 
---所在文件: scripts/metaclass.lua
function MetaClass(base,_ctor)
end

---
---UNKNOWN
---
---@param results idk 
---所在文件: scripts/perfutil.lua
function GetProfilerModInfo(results)
end

---
---UNKNOWN
---
---所在文件: scripts/perfutil.lua
function GetProfilerMetaData()
end

---
---UNKNOWN
---
---所在文件: scripts/perfutil.lua
function ExpandWorldFromProfile()
end

---
---UNKNOWN
---
---@param results idk 
---所在文件: scripts/perfutil.lua
function GetProfilerSave(results)
end

---
---UNKNOWN
---
---@param results idk 
---所在文件: scripts/perfutil.lua
function GetProfilerServerStats(results)
end

---
---UNKNOWN
---
---所在文件: scripts/perfutil.lua
function CountEntities()
end

---
---UNKNOWN
---
---@param results idk 
---所在文件: scripts/perfutil.lua
function GetProfilerPlayers(results)
end

---
---UNKNOWN
---
---@param p0x idk 
---@param p0y idk 
---@param p1x idk 
---@param p1y idk 
---所在文件: scripts/ocean_util.lua
function FindLandBetweenPoints(p0x,p0y,p1x,p1y)
end

---
---UNKNOWN
---
---@param inst idk 
---@param target idk 
---@param max_distance idk 
---所在文件: scripts/ocean_util.lua
function CanProbablyReachTargetFromShore(inst,target,max_distance)
end

---
---UNKNOWN
---
---@param entity idk 
---所在文件: scripts/ocean_util.lua
function SinkEntity(entity)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/ocean_util.lua
function TintByOceanTile(inst)
end

---
---UNKNOWN
---
---@param tile idk 
---所在文件: scripts/ocean_util.lua
function IsLandTile(tile)
end

---
---UNKNOWN
---
---@param entity idk 
---@param entity_sinks_in_water idk 
---所在文件: scripts/ocean_util.lua
function ShouldEntitySink(entity,entity_sinks_in_water)
end

---
---UNKNOWN
---
---@param pt idk 
---所在文件: scripts/ocean_util.lua
function GetOceanDepthAtPoint(pt)
end

---
---UNKNOWN
---
---@param tile idk 
---所在文件: scripts/ocean_util.lua
function IsOceanTile(tile)
end

---
---UNKNOWN
---
---@param creature idk 
---所在文件: scripts/ocean_util.lua
function LandFlyingCreature(creature)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param excludeclosest idk 
---所在文件: scripts/ocean_util.lua
function FindRandomPointOnShoreFromOcean(x,y,z,excludeclosest)
end

---
---UNKNOWN
---
---@param creature idk 
---所在文件: scripts/ocean_util.lua
function RaiseFlyingCreature(creature)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/ocean_util.lua
function GetOceanDepthAtPosition(x,y,z)
end

---
---UNKNOWN
---
---@param position idk 
---@param rotation idk 
---@param spawn_radius idk 
---@param numWaves idk 
---@param totalAngle idk 
---@param waveSpeed idk 
---@param wavePrefab idk 
---@param idleTime idk 
---@param instantActive idk 
---所在文件: scripts/ocean_util.lua
function SpawnAttackWaves(position,rotation,spawn_radius,numWaves,totalAngle,waveSpeed,wavePrefab,idleTime,instantActive)
end

---
---UNKNOWN
---
---@param position idk 
---@param rotation idk 
---@param waveSpeed idk 
---@param wavePrefab idk 
---@param idleTime idk 
---@param instantActive idk 
---所在文件: scripts/ocean_util.lua
function SpawnAttackWave(position,rotation,waveSpeed,wavePrefab,idleTime,instantActive)
end

---
---UNKNOWN
---
---@param serverIp idk 
---@param serverPort idk 
---@param serverPassword idk 
---@param serverNetId idk 
---所在文件: scripts/networking.lua
function MigrateToServer(serverIp,serverPort,serverPassword,serverNetId)
end

---
---UNKNOWN
---
---@param user_id idk 
---所在文件: scripts/networking.lua
function VerifySpawnNewPlayerOnServerRequest(user_id)
end

---
---UNKNOWN
---
---@param username idk 
---@param message idk 
---@param colour idk 
---所在文件: scripts/networking.lua
function OnTwitchMessageReceived(username,message,colour)
end

---
---UNKNOWN
---
---@param server idk 
---所在文件: scripts/networking.lua
function CalcQuickJoinServerScore(server)
end

---
---UNKNOWN
---
---@param name idk 
---@param colour idk 
---所在文件: scripts/networking.lua
function Networking_JoinAnnouncement(name,colour)
end

---
---UNKNOWN
---
---@param caller idk 
---@param target idk 
---所在文件: scripts/networking.lua
function Networking_KickMetricsEvent(caller,target)
end

---
---UNKNOWN
---
---@param mod idk 
---所在文件: scripts/networking.lua
function Networking_ModOutOfDateAnnouncement(mod)
end

---
---UNKNOWN
---
---@param name idk 
---@param colour idk 
---所在文件: scripts/networking.lua
function Networking_LeaveAnnouncement(name,colour)
end

---
---UNKNOWN
---
---@param caller idk 
---所在文件: scripts/networking.lua
function Networking_RegenerateMetricsEvent(caller)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function Networking_PartyChanged()
end

---
---UNKNOWN
---
---@param ip idk 
---@param port idk 
---所在文件: scripts/networking.lua
function Networking_PartyServer(ip,port)
end

---
---UNKNOWN
---
---@param chatline idk 
---所在文件: scripts/networking.lua
function Networking_PartyChat(chatline)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/networking.lua
function Networking_Announcement_GetDisplayName(name)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function Networking_LeftParty()
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function Networking_JoinedParty()
end

---
---UNKNOWN
---
---@param status idk 
---所在文件: scripts/networking.lua
function OnTwitchChatStatusUpdate(status)
end

---
---UNKNOWN
---
---@param data idk 
---@param session_identifier idk 
---@param callback idk 
---@param metadataStr idk 
---所在文件: scripts/networking.lua
function SerializeWorldSession(data,session_identifier,callback,metadataStr)
end

---
---UNKNOWN
---
---@param widg idk 
---所在文件: scripts/networking.lua
function RegisterFriendsManager(widg)
end

---
---UNKNOWN
---
---@param userid idk 
---所在文件: scripts/networking.lua
function ClientDisconnected(userid)
end

---
---UNKNOWN
---
---@param name idk 
---@param colour idk 
---所在文件: scripts/networking.lua
function Networking_BanAnnouncement(name,colour)
end

---
---UNKNOWN
---
---@param userid idk 
---@param items idk 
---@param item_counts idk 
---@param users idk 
---@param cb idk 
---所在文件: scripts/networking.lua
function ReportAction(userid,items,item_counts,users,cb)
end

---
---UNKNOWN
---
---@param userid idk 
---所在文件: scripts/networking.lua
function ClientAuthenticationComplete(userid)
end

---
---UNKNOWN
---
---@param name idk 
---@param colour idk 
---所在文件: scripts/networking.lua
function Networking_KickAnnouncement(name,colour)
end

---
---UNKNOWN
---
---@param guid idk 
---@param message idk 
---@param duration idk 
---@param text_filter_context idk 
---@param original_author idk 
---所在文件: scripts/networking.lua
function Networking_Talk(guid,message,duration,text_filter_context,original_author)
end

---
---UNKNOWN
---
---@param userid idk 
---所在文件: scripts/networking.lua
function LookupPlayerInstByUserID(userid)
end

---
---UNKNOWN
---
---@param user_name idk 
---@param user_colour idk 
---@param skin_name idk 
---所在文件: scripts/networking.lua
function Networking_SkinAnnouncement(user_name,user_colour,skin_name)
end

---
---UNKNOWN
---
---@param userid idk 
---@param name idk 
---@param prefab idk 
---@param colour idk 
---@param rolls idk 
---@param max idk 
---所在文件: scripts/networking.lua
function Networking_RollAnnouncement(userid,name,prefab,colour,rolls,max)
end

---
---UNKNOWN
---
---@param caller idk 
---@param target idk 
---所在文件: scripts/networking.lua
function Networking_BanMetricsEvent(caller,target)
end

---
---UNKNOWN
---
---@param user_id idk 
---@param prefab_name idk 
---@param skin idk 
---所在文件: scripts/networking.lua
function ValidateRecipeSkinRequest(user_id,prefab_name,skin)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function JoinServerFilter()
end

---
---UNKNOWN
---
---@param commandid idk 
---@param targetname idk 
---@param passed idk 
---所在文件: scripts/networking.lua
function Networking_VoteAnnouncement(commandid,targetname,passed)
end

---
---UNKNOWN
---
---@param message idk 
---所在文件: scripts/networking.lua
function Networking_SystemMessage(message)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function ShowConnectingToGamePopup()
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function StartDedicatedServer()
end

---
---UNKNOWN
---
---@param message idk 
---@param colour idk 
---@param announce_type idk 
---所在文件: scripts/networking.lua
function Networking_Announcement(message,colour,announce_type)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function GetDefaultServerData()
end

---
---UNKNOWN
---
---@param caller idk 
---所在文件: scripts/networking.lua
function Networking_RollbackMetricsEvent(caller)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function UpdateServerWorldGenDataString()
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function SpawnSecondInstance()
end

---
---UNKNOWN
---
---@param guid idk 
---@param userid idk 
---@param name idk 
---@param prefab idk 
---@param message idk 
---@param colour idk 
---@param whisper idk 
---@param isemote idk 
---@param user_vanity idk 
---所在文件: scripts/networking.lua
function Networking_Say(guid,userid,name,prefab,message,colour,whisper,isemote,user_vanity)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function UpdateServerTagsString()
end

---
---UNKNOWN
---
---@param count idk 
---所在文件: scripts/networking.lua
function WorldRollbackFromSim(count)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function WorldResetFromSim()
end

---
---UNKNOWN
---
---@param message idk 
---@param colour idk 
---所在文件: scripts/networking.lua
function Networking_DeathAnnouncement(message,colour)
end

---
---UNKNOWN
---
---@param guid idk 
---@param userid idk 
---@param cmd idk 
---所在文件: scripts/networking.lua
function Networking_SlashCmd(guid,userid,cmd)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function GetAvailablePlayerColours()
end

---
---UNKNOWN
---
---@param inviter idk 
---@param partyid idk 
---所在文件: scripts/networking.lua
function Networking_PartyInvite(inviter,partyid)
end

---
---UNKNOWN
---
---@param userid idk 
---@param prefab_name idk 
---@param skin_base idk 
---@param clothing_body idk 
---@param clothing_hand idk 
---@param clothing_legs idk 
---@param clothing_feet idk 
---所在文件: scripts/networking.lua
function RequestedLobbyCharacter(userid,prefab_name,skin_base,clothing_body,clothing_hand,clothing_legs,clothing_feet)
end

---
---UNKNOWN
---
---@param player_guid idk 
---@param skin_base idk 
---@param clothing_body idk 
---@param clothing_hand idk 
---@param clothing_legs idk 
---@param clothing_feet idk 
---@param starting_item_skins idk 
---@param skillselection idk 
---所在文件: scripts/networking.lua
function SpawnNewPlayerOnServerFromSim(player_guid,skin_base,clothing_body,clothing_hand,clothing_legs,clothing_feet,starting_item_skins,skillselection)
end

---
---UNKNOWN
---
---@param user_id idk 
---@param prefab_name idk 
---@param skin_base idk 
---@param clothing_body idk 
---@param clothing_hand idk 
---@param clothing_legs idk 
---@param clothing_feet idk 
---@param allow_seamlessswap_characters idk 
---所在文件: scripts/networking.lua
function ValidateSpawnPrefabRequest(user_id,prefab_name,skin_base,clothing_body,clothing_hand,clothing_legs,clothing_feet,allow_seamlessswap_characters)
end

---
---UNKNOWN
---
---@param player idk 
---@param isnewspawn idk 
---所在文件: scripts/networking.lua
function SerializeUserSession(player,isnewspawn)
end

---
---UNKNOWN
---
---@param server_listing idk 
---所在文件: scripts/networking.lua
function DownloadMods(server_listing)
end

---
---UNKNOWN
---
---@param player_guid idk 
---@param old_player_guid idk 
---@param skin_base idk 
---@param clothing_body idk 
---@param clothing_hand idk 
---@param clothing_legs idk 
---@param clothing_feet idk 
---所在文件: scripts/networking.lua
function SpawnSeamlessPlayerReplacementFromSim(player_guid,old_player_guid,skin_base,clothing_body,clothing_hand,clothing_legs,clothing_feet)
end

---
---UNKNOWN
---
---@param player idk 
---所在文件: scripts/networking.lua
function DeleteUserSession(player)
end

---
---UNKNOWN
---
---@param server_listing idk 
---@param optional_password_override idk 
---所在文件: scripts/networking.lua
function JoinServer(server_listing,optional_password_override)
end

---
---UNKNOWN
---
---所在文件: scripts/networking.lua
function GetPlayerClientTable()
end

---
---UNKNOWN
---
---@param success idk 
---@param result idk 
---所在文件: scripts/networking.lua
function OnTwitchLoginAttempt(success,result)
end

---
---UNKNOWN
---
---@param message idk 
---@param colour idk 
---所在文件: scripts/networking.lua
function Networking_ResurrectAnnouncement(message,colour)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/debugcommands.lua
function d_skin_name(name)
end

---
---UNKNOWN
---
---@param param idk 
---所在文件: scripts/debugcommands.lua
function d_test_thank_you(param)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_particles()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_potions()
end

---
---UNKNOWN
---
---@param time idk 
---@param target idk 
---所在文件: scripts/debugcommands.lua
function d_testdps(time,target)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_unlockallachievements()
end

---
---UNKNOWN
---
---@param c1 idk 
---@param c2 idk 
---@param c3 idk 
---所在文件: scripts/debugcommands.lua
function d_light(c1,c2,c3)
end

---
---UNKNOWN
---
---@param spellnum idk 
---@param item idk 
---所在文件: scripts/debugcommands.lua
function d_spell(spellnum,item)
end

---
---UNKNOWN
---
---@param state idk 
---所在文件: scripts/debugcommands.lua
function d_teststate(state)
end

---
---UNKNOWN
---
---@param speed idk 
---@param stamina idk 
---@param direction idk 
---@param reaction idk 
---所在文件: scripts/debugcommands.lua
function d_ratracer(speed,stamina,direction,reaction)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_portalfx()
end

---
---UNKNOWN
---
---@param loopname idk 
---所在文件: scripts/debugcommands.lua
function d_stopsound(loopname)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_spawnallhandequipment_onstands()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_dumpCreatureTXT()
end

---
---UNKNOWN
---
---@param character idk 
---所在文件: scripts/debugcommands.lua
function d_printskilltreestringsforcharacter(character)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allmutators()
end

---
---UNKNOWN
---
---@param count_cutoff idk 
---@param item_cutoff idk 
---@param density_cutoff idk 
---所在文件: scripts/debugcommands.lua
function d_mapstatistics(count_cutoff,item_cutoff,density_cutoff)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_boatracepointers()
end

---
---UNKNOWN
---
---@param grow_stage idk 
---@param oversized idk 
---所在文件: scripts/debugcommands.lua
function d_farmplants(grow_stage,oversized)
end

---
---UNKNOWN
---
---@param param idk 
---所在文件: scripts/debugcommands.lua
function d_test_skins_gift(param)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_testwalls()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_lunarrift()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_gofishing()
end

---
---UNKNOWN
---
---@param soundpath idk 
---@param loopname idk 
---@param volume idk 
---所在文件: scripts/debugcommands.lua
function d_testsound(soundpath,loopname,volume)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_startmoonstorm()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_craftingstations()
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/debugcommands.lua
function d_teleportboat(x,y,z)
end

---
---UNKNOWN
---
---@param x idk 
---@param y idk 
---@param z idk 
---@param lifetime idk 
---@param prefab idk 
---所在文件: scripts/debugcommands.lua
function d_timeddebugprefab(x,y,z,lifetime,prefab)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allkitcoons()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_getwidget()
end

---
---UNKNOWN
---
---@param file idk 
---所在文件: scripts/debugcommands.lua
function d_require(file)
end

---
---UNKNOWN
---
---@param radius idk 
---@param num idk 
---@param lifetime idk 
---所在文件: scripts/debugcommands.lua
function d_radius(radius,num,lifetime)
end

---
---UNKNOWN
---
---@param bitswanted idk 
---所在文件: scripts/debugcommands.lua
function d_testhashes_prefabs(bitswanted)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_waxwellworker()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_riftspawns()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_printscrapbookrepairmaterialsdata()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allfish()
end

---
---UNKNOWN
---
---@param domestication idk 
---@param obedience idk 
---所在文件: scripts/debugcommands.lua
function d_domestication(domestication,obedience)
end

---
---UNKNOWN
---
---@param plant idk 
---所在文件: scripts/debugcommands.lua
function d_waxplant(plant)
end

---
---UNKNOWN
---
---@param entry idk 
---所在文件: scripts/debugcommands.lua
function d_erasescrapbookentrydata(entry)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_cookbook()
end

---
---UNKNOWN
---
---@param print_missing_icons idk 
---@param noreset idk 
---所在文件: scripts/debugcommands.lua
function d_createscrapbookdata(print_missing_icons,noreset)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_showalleventservers()
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/debugcommands.lua
function d_clothing(name)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_combatgear()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_fishing()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_islandstart()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_spiders()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_dumpItemsTXT()
end

---
---UNKNOWN
---
---@param dialog idk 
---@param banter_line idk 
---所在文件: scripts/debugcommands.lua
function d_lavaarena_speech(dialog,banter_line)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allstscostumes()
end

---
---UNKNOWN
---
---@param ground idk 
---@param pt idk 
---所在文件: scripts/debugcommands.lua
function d_ground(ground,pt)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_skilltreestats()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allshells()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_punchingbags()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_moonplant()
end

---
---UNKNOWN
---
---@param chain idk 
---所在文件: scripts/debugcommands.lua
function d_daywalker(chain)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_ratracers()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_waxwellprotector()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_resetruins()
end

---
---UNKNOWN
---
---@param prefab idk 
---@param count idk 
---@param force idk 
---所在文件: scripts/debugcommands.lua
function d_combatsimulator(prefab,count,force)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allpillows_onstands()
end

---
---UNKNOWN
---
---@param animname idk 
---@param loop idk 
---所在文件: scripts/debugcommands.lua
function d_anim(animname,loop)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allpillows()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_spawnallarmor_onstands()
end

---
---UNKNOWN
---
---@param path idk 
---所在文件: scripts/debugcommands.lua
function d_decodedata(path)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/debugcommands.lua
function d_spawnequipment_onstand(...)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_spawnallhats()
end

---
---UNKNOWN
---
---@param filename idk 
---@param spacing idk 
---所在文件: scripts/debugcommands.lua
function d_spawnfilelist(filename,spacing)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_shadowrift()
end

---
---UNKNOWN
---
---@param param idk 
---所在文件: scripts/debugcommands.lua
function d_test_skins_announce(param)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_recipecards()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_resetskilltree()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_wintersfood()
end

---
---UNKNOWN
---
---@param prefab idk 
---@param nugget_count idk 
---所在文件: scripts/debugcommands.lua
function d_prizepouch(prefab,nugget_count)
end

---
---UNKNOWN
---
---@param material idk 
---所在文件: scripts/debugcommands.lua
function d_statues(material)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_unlockscrapbook()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_lavaarena_skip()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_seeds()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_stopmoonstorm()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_moonaltars()
end

---
---UNKNOWN
---
---@param type idk 
---所在文件: scripts/debugcommands.lua
function d_clothing_clear(type)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_stalkersetup()
end

---
---UNKNOWN
---
---@param width idk 
---@param height idk 
---所在文件: scripts/debugcommands.lua
function d_walls(width,height)
end

---
---UNKNOWN
---
---@param other_ku idk 
---所在文件: scripts/debugcommands.lua
function d_reportevent(other_ku)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_hidekitcoons()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_cycle_clothing()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allheavy()
end

---
---UNKNOWN
---
---@param plant idk 
---@param num_wide idk 
---@param grow_stage idk 
---@param spacing idk 
---所在文件: scripts/debugcommands.lua
function d_plant(plant,num_wide,grow_stage,spacing)
end

---
---UNKNOWN
---
---@param swim idk 
---@param r idk 
---@param g idk 
---@param b idk 
---所在文件: scripts/debugcommands.lua
function d_fish(swim,r,g,b)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allcustomhidingspots()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_sinkhole()
end

---
---UNKNOWN
---
---@param bitswanted idk 
---@param tests idk 
---所在文件: scripts/debugcommands.lua
function d_testhashes_random(bitswanted,tests)
end

---
---UNKNOWN
---
---@param name idk 
---@param offset idk 
---所在文件: scripts/debugcommands.lua
function d_spawnlayout(name,offset)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_turfs()
end

---
---UNKNOWN
---
---@param reuse idk 
---@param out_file_name idk 
---所在文件: scripts/debugcommands.lua
function d_setup_placeholders(reuse,out_file_name)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_weirdfloaters()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_spawnallhats_onstands()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_halloween()
end

---
---UNKNOWN
---
---@param param idk 
---所在文件: scripts/debugcommands.lua
function d_test_skins_popup(param)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_reloadskilltreedefs()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_giveturfs()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_fertilizers()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_hidekitcoon()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_oversized()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_togglelunarhail()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_unlockfoodachievements()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_structuresTXT()
end

---
---UNKNOWN
---
---@param mode idk 
---所在文件: scripts/debugcommands.lua
function d_skin_mode(mode)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_testruins()
end

---
---UNKNOWN
---
---@param networkid idk 
---@param x idk 
---@param y idk 
---@param z idk 
---所在文件: scripts/debugcommands.lua
function d_removeentitywithnetworkid(networkid,x,y,z)
end

---
---UNKNOWN
---
---@param list idk 
---@param spacing idk 
---@param fn idk 
---所在文件: scripts/debugcommands.lua
function d_spawnlist(list,spacing,fn)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_wintersfeast()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_boatitems()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_tables()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_print_skin_info()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_hunt()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_playeritems()
end

---
---UNKNOWN
---
---@param prefab idk 
---@param scenario idk 
---所在文件: scripts/debugcommands.lua
function d_spawn_ds(prefab,scenario)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_madsciencemats()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_checkmissingscrapbookentries()
end

---
---UNKNOWN
---
---@param tendency idk 
---@param saddle idk 
---所在文件: scripts/debugcommands.lua
function d_domesticatedbeefalo(tendency,saddle)
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allsongs()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_oceanarena()
end

---
---UNKNOWN
---
---所在文件: scripts/debugcommands.lua
function d_allcircuits()
end

---
---UNKNOWN
---
---@param tick idk 
---@param namespace idk 
---@param code idk 
---@param data idk 
---所在文件: scripts/networkclientrpc.lua
function HandleClientModRPC(tick,namespace,code,data)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function optstring(val)
end

---
---UNKNOWN
---
---@param tick idk 
---@param code idk 
---@param data idk 
---所在文件: scripts/networkclientrpc.lua
function HandleClientRPC(tick,code,data)
end

---
---UNKNOWN
---
---@param sender idk 
---@param tick idk 
---@param code idk 
---@param data idk 
---所在文件: scripts/networkclientrpc.lua
function HandleShardRPC(sender,tick,code,data)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function checkentity(val)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function optuint(val)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function checkstring(val)
end

---
---UNKNOWN
---
---@param sender idk 
---@param tick idk 
---@param namespace idk 
---@param code idk 
---@param data idk 
---所在文件: scripts/networkclientrpc.lua
function HandleShardModRPC(sender,tick,namespace,code,data)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function checkuint(val)
end

---
---UNKNOWN
---
---所在文件: scripts/networkclientrpc.lua
function DisableRPCSending()
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function checkbool(val)
end

---
---UNKNOWN
---
---@param sender idk 
---@param tick idk 
---@param code idk 
---@param data idk 
---所在文件: scripts/networkclientrpc.lua
function HandleRPC(sender,tick,code,data)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function optnumber(val)
end

---
---UNKNOWN
---
---@param code idk 
---@param ... idk 
---所在文件: scripts/networkclientrpc.lua
function SendRPCToServer(code,...)
end

---
---UNKNOWN
---
---@param namespace idk 
---@param name idk 
---所在文件: scripts/networkclientrpc.lua
function MarkUserIDRPC(namespace,name)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function optentity(val)
end

---
---UNKNOWN
---
---所在文件: scripts/networkclientrpc.lua
function HandleRPCQueue()
end

---
---UNKNOWN
---
---所在文件: scripts/networkclientrpc.lua
function TickRPCQueue()
end

---
---UNKNOWN
---
---@param sender idk 
---@param tick idk 
---@param namespace idk 
---@param code idk 
---@param data idk 
---所在文件: scripts/networkclientrpc.lua
function HandleModRPC(sender,tick,namespace,code,data)
end

---
---UNKNOWN
---
---@param code idk 
---@param ... idk 
---所在文件: scripts/networkclientrpc.lua
function SendRPCToShard(code,...)
end

---
---UNKNOWN
---
---@param code idk 
---@param ... idk 
---所在文件: scripts/networkclientrpc.lua
function SendRPCToClient(code,...)
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/networkclientrpc.lua
function checknumber(val)
end

---
---UNKNOWN
---
---@param assets idk 
---@param modname idk 
---所在文件: scripts/mainfunctions.lua
function ModReloadFrontEndAssets(assets,modname)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetStaticTime()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function JapaneseOnPS4()
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/mainfunctions.lua
function LoadAchievements(filename)
end

---
---UNKNOWN
---
---@param name idk 
---@param data idk 
---@param encode idk 
---@param callback idk 
---所在文件: scripts/mainfunctions.lua
function SavePersistentString(name,data,encode,callback)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetTimeReal()
end

---
---UNKNOWN
---
---@param achievements idk 
---所在文件: scripts/mainfunctions.lua
function RegisterAchievements(achievements)
end

---
---UNKNOWN
---
---@param modname idk 
---所在文件: scripts/mainfunctions.lua
function ModUnloadPreloadAssets(modname)
end

---
---UNKNOWN
---
---@param autopause idk 
---所在文件: scripts/mainfunctions.lua
function SetAutopaused(autopause)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetTick()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GlobalInit()
end

---
---UNKNOWN
---
---@param msg_verbosity idk 
---@param ... idk 
---所在文件: scripts/mainfunctions.lua
function Print(msg_verbosity,...)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function IsInFrontEnd()
end

---
---UNKNOWN
---
---@param data idk 
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function ResumeExistingUserSession(data,guid)
end

---
---UNKNOWN
---
---@param pause idk 
---所在文件: scripts/mainfunctions.lua
function SetServerPaused(pause)
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function OnPhysicsSleep(guid)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/mainfunctions.lua
function AwardFrontendAchievement(name)
end

---
---UNKNOWN
---
---@param name idk 
---@param callback idk 
---所在文件: scripts/mainfunctions.lua
function ErasePersistentString(name,callback)
end

---
---UNKNOWN
---
---@param calculatedhashes idk 
---所在文件: scripts/mainfunctions.lua
function DataBundleFileHashes(calculatedhashes)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function BeginDataBundleFileHashes()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetTimeRealSeconds()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function ShowBadHashUI()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function Start()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function ForceAssetReset()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function IsSimPaused()
end

---
---UNKNOWN
---
---@param error idk 
---所在文件: scripts/mainfunctions.lua
function DisplayError(error)
end

---
---UNKNOWN
---
---@param repeat_time idk 
---@param lowered_volume_percent idk 
---所在文件: scripts/mainfunctions.lua
function CreateRepeatedSoundVolumeReduction(repeat_time,lowered_volume_percent)
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function DisableLoadingProtection(guid)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function OnSimUnpaused()
end

---
---UNKNOWN
---
---@param player_guid idk 
---@param expected idk 
---所在文件: scripts/mainfunctions.lua
function OnPlayerLeave(player_guid,expected)
end

---
---UNKNOWN
---
---@param assets idk 
---@param modname idk 
---所在文件: scripts/mainfunctions.lua
function ModPreloadAssets(assets,modname)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function SaveAndShutdown()
end

---
---UNKNOWN
---
---@param name idk 
---@return ent
---所在文件: scripts/mainfunctions.lua
function CreateEntity(name)
end

---
---UNKNOWN
---
---@param prefab idk 
---所在文件: scripts/mainfunctions.lua
function RegisterSinglePrefab(prefab)
end

---
---UNKNOWN
---
---@param tagsTable idk 
---所在文件: scripts/mainfunctions.lua
function BuildTagsStringCommon(tagsTable)
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function RemoveEntity(guid)
end

---
---UNKNOWN
---
---@param cb idk 
---所在文件: scripts/mainfunctions.lua
function DoLoadingPortal(cb)
end

---
---UNKNOWN
---
---@param listener idk 
---所在文件: scripts/mainfunctions.lua
function RegisterOnAccountEventListener(listener)
end

---
---UNKNOWN
---
---@param original_inst idk 
---@param name idk 
---@param skin idk 
---@param skin_id idk 
---@param creator idk 
---所在文件: scripts/mainfunctions.lua
function ReplacePrefab(original_inst,name,skin,skin_id,creator)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function OnDynamicCloudSyncDelete()
end

---
---UNKNOWN
---
---@param loading_state idk 
---@param match_results idk 
---所在文件: scripts/mainfunctions.lua
function NotifyLoadingState(loading_state,match_results)
end

---
---UNKNOWN
---
---@param in_params idk 
---所在文件: scripts/mainfunctions.lua
function StartNextInstance(in_params)
end

---
---UNKNOWN
---
---@param nisname idk 
---@param lines idk 
---所在文件: scripts/mainfunctions.lua
function PlayNIS(nisname,lines)
end

---
---UNKNOWN
---
---@param name idk 
---@param value idk 
---@param player idk 
---所在文件: scripts/mainfunctions.lua
function NotifyPlayerProgress(name,value,player)
end

---
---UNKNOWN
---
---@param fnstr idk 
---@param guid idk 
---@param x idk 
---@param z idk 
---所在文件: scripts/mainfunctions.lua
function ExecuteConsoleCommand(fnstr,guid,x,z)
end

---
---UNKNOWN
---
---@param filename idk 
---@param async_batch_validation idk 
---@param search_asset_first_path idk 
---所在文件: scripts/mainfunctions.lua
function LoadPrefabFile(filename,async_batch_validation,search_asset_first_path)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function IsPaused()
end

---
---UNKNOWN
---
---@param success idk 
---@param event_code idk 
---@param custom_message idk 
---所在文件: scripts/mainfunctions.lua
function OnAccountEvent(success,event_code,custom_message)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetTime()
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/mainfunctions.lua
function LoadScript(filename)
end

---
---UNKNOWN
---
---@param sessionid idk 
---@param userid idk 
---所在文件: scripts/mainfunctions.lua
function RestoreSnapshotUserSession(sessionid,userid)
end

---
---UNKNOWN
---
---@param listener_to_remove idk 
---所在文件: scripts/mainfunctions.lua
function RemoveOnAccountEventListener(listener_to_remove)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function LoadFonts()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetExtendedDebugString()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetStaticTick()
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/mainfunctions.lua
function SpawnPrefabFromSim(name)
end

---
---UNKNOWN
---
---@param success idk 
---所在文件: scripts/mainfunctions.lua
function ResumeRequestLoadComplete(success)
end

---
---UNKNOWN
---
---@param save idk 
---所在文件: scripts/mainfunctions.lua
function DoRestart(save)
end

---
---生成预制物
---
---@param name PrefabID 
---@param skin idk 
---@param skin_id idk 
---@param creator idk 
---@return ent
---所在文件: scripts/mainfunctions.lua
function SpawnPrefab(name,skin,skin_id,creator)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function OnFocusGained()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function OnFocusLost()
end

---
---UNKNOWN
---
---@param bg idk 
---所在文件: scripts/mainfunctions.lua
function TintBackground(bg)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function UnloadFonts()
end

---
---UNKNOWN
---
---@param autopause idk 
---所在文件: scripts/mainfunctions.lua
function SetConsoleAutopaused(autopause)
end

---
---UNKNOWN
---
---@param name idk 
---所在文件: scripts/mainfunctions.lua
function PrefabExists(name)
end

---
---UNKNOWN
---
---@param val idk 
---@param reason idk 
---所在文件: scripts/mainfunctions.lua
function SetPause(val,reason)
end

---
---UNKNOWN
---
---@param message idk 
---所在文件: scripts/mainfunctions.lua
function ProcessJsonMessage(message)
end

---
---UNKNOWN
---
---@param message idk 
---所在文件: scripts/mainfunctions.lua
function OnPushPopupDialog(message)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/mainfunctions.lua
function RegisterPrefabs(...)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function OnSimPaused()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function OnDynamicCloudSyncReload()
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function GetEntityString(guid)
end

---
---UNKNOWN
---
---@param autopause idk 
---所在文件: scripts/mainfunctions.lua
function SetCraftingAutopaused(autopause)
end

---
---UNKNOWN
---
---@param saved idk 
---@param newents idk 
---所在文件: scripts/mainfunctions.lua
function SpawnSaveRecord(saved,newents)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function IsMigrating()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function InGamePlay()
end

---
---UNKNOWN
---
---@param map_name idk 
---所在文件: scripts/mainfunctions.lua
function LoadMapFile(map_name)
end

---
---UNKNOWN
---
---@param pause idk 
---所在文件: scripts/mainfunctions.lua
function SetPauseFromCode(pause)
end

---
---UNKNOWN
---
---@param total_seconds idk 
---所在文件: scripts/mainfunctions.lua
function SecondsToTimeString(total_seconds)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function Shutdown()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function DoWorldOverseerShutdown()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function RequestShutdown()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function OnDemoTimeout()
end

---
---UNKNOWN
---
---@param notification idk 
---所在文件: scripts/mainfunctions.lua
function DisplayAntiAddictionNotification(notification)
end

---
---UNKNOWN
---
---@param message idk 
---@param should_reset idk 
---@param force_immediate_reset idk 
---@param details idk 
---@param miscdata idk 
---所在文件: scripts/mainfunctions.lua
function OnNetworkDisconnect(message,should_reset,force_immediate_reset,details,miscdata)
end

---
---UNKNOWN
---
---@param modname idk 
---所在文件: scripts/mainfunctions.lua
function ModUnloadFrontEndAssets(modname)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetDebugEntity()
end

---
---UNKNOWN
---
---@param data idk 
---所在文件: scripts/mainfunctions.lua
function ParseUserSessionData(data)
end

---
---UNKNOWN
---
---@param instanceparameters idk 
---所在文件: scripts/mainfunctions.lua
function SimReset(instanceparameters)
end

---
---UNKNOWN
---
---@param isshutdown idk 
---@param cb idk 
---所在文件: scripts/mainfunctions.lua
function SaveGame(isshutdown,cb)
end

---
---UNKNOWN
---
---@param settings idk 
---所在文件: scripts/mainfunctions.lua
function SetInstanceParameters(settings)
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function OnEntitySleep(guid)
end

---
---UNKNOWN
---
---@param purchases idk 
---所在文件: scripts/mainfunctions.lua
function SetPurchases(purchases)
end

---
---UNKNOWN
---
---@param name idk 
---@param pos idk 
---@param radius idk 
---所在文件: scripts/mainfunctions.lua
function AwardRadialAchievement(name,pos,radius)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function DoAutopause()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetTickTime()
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetDebugTable()
end

---
---UNKNOWN
---
---@param val idk 
---所在文件: scripts/mainfunctions.lua
function SetSimPause(val)
end

---
---UNKNOWN
---
---@param inst idk 
---所在文件: scripts/mainfunctions.lua
function SetDebugEntity(inst)
end

---
---UNKNOWN
---
---@param prefab idk 
---@param resolve_fn idk 
---所在文件: scripts/mainfunctions.lua
function RegisterPrefabsImpl(prefab,resolve_fn)
end

---
---UNKNOWN
---
---@param guid idk 
---@param event idk 
---@param data idk 
---所在文件: scripts/mainfunctions.lua
function PushEntityEvent(guid,event,data)
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function OnEntityWake(guid)
end

---
---UNKNOWN
---
---所在文件: scripts/mainfunctions.lua
function GetDebugString()
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function ReplicateEntity(guid)
end

---
---UNKNOWN
---
---@param pause idk 
---@param autopause idk 
---@param gameautopause idk 
---@param source idk 
---所在文件: scripts/mainfunctions.lua
function OnServerPauseDirty(pause,autopause,gameautopause,source)
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function OnPhysicsWake(guid)
end

---
---UNKNOWN
---
---@param name idk 
---@param level idk 
---@param days idk 
---@param player idk 
---所在文件: scripts/mainfunctions.lua
function NotifyPlayerPresence(name,level,days,player)
end

---
---UNKNOWN
---
---@param scale idk 
---所在文件: scripts/mainfunctions.lua
function SetDefaultTimeScale(scale)
end

---
---UNKNOWN
---
---@param tbl idk 
---所在文件: scripts/mainfunctions.lua
function SetDebugTable(tbl)
end

---
---UNKNOWN
---
---@param filename idk 
---@param assettype idk 
---所在文件: scripts/mainfunctions.lua
function ShouldIgnoreResolve(filename,assettype)
end

---
---UNKNOWN
---
---@param guid idk 
---所在文件: scripts/mainfunctions.lua
function GetEntityDisplayName(guid)
end

---
---UNKNOWN
---
---@param button idk 
---所在文件: scripts/mainfunctions.lua
function HookLoginButtonForDataBundleFileHashes(button)
end

---
---UNKNOWN
---
---@param entityguid idk 
---所在文件: scripts/mainfunctions.lua
function OnRemoveEntity(entityguid)
end

---
---UNKNOWN
---
---@param name idk 
---@param player idk 
---所在文件: scripts/mainfunctions.lua
function AwardPlayerAchievement(name,player)
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/mainfunctions.lua
function RunScript(filename)
end

---
---UNKNOWN
---
---@param recname idk 
---所在文件: scripts/recipe.lua
function GetValidRecipe(recname)
end

---
---UNKNOWN
---
---@param recname idk 
---所在文件: scripts/recipe.lua
function IsRecipeValid(recname)
end

---
---UNKNOWN
---
---所在文件: scripts/recipe.lua
function RemoveAllRecipes()
end

---
---UNKNOWN
---
---@param ingredienttype idk 
---所在文件: scripts/recipe.lua
function IsTechIngredient(ingredienttype)
end

---
---UNKNOWN
---
---@param ingredienttype idk 
---所在文件: scripts/recipe.lua
function IsCharacterIngredient(ingredienttype)
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsNotConsole()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function GetStaticTime()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function GetStaticTick()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsXB1()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsPS4()
end

---
---UNKNOWN
---
---@param savedata idk 
---所在文件: scripts/worldgen_main.lua
function CheckMapSaveData(savedata)
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function GetTimeReal()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function GetTickTime()
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/worldgen_main.lua
function loadfile(filename)
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function GetTick()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsConsole()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsLinux()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function GetDebugString()
end

---
---UNKNOWN
---
---@param debug idk 
---所在文件: scripts/worldgen_main.lua
function PROFILE_world_gen(debug)
end

---
---UNKNOWN
---
---@param savedata idk 
---所在文件: scripts/worldgen_main.lua
function ShowDebug(savedata)
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsRail()
end

---
---UNKNOWN
---
---@param seed idk 
---所在文件: scripts/worldgen_main.lua
function SetWorldGenSeed(seed)
end

---
---UNKNOWN
---
---@param debug idk 
---@param world_gen_data idk 
---所在文件: scripts/worldgen_main.lua
function GenerateNew(debug,world_gen_data)
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsSteam()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function IsSteamDeck()
end

---
---UNKNOWN
---
---所在文件: scripts/worldgen_main.lua
function GetTime()
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/worldgen_main.lua
function LoadScript(filename)
end

---
---UNKNOWN
---
---@param filename idk 
---所在文件: scripts/worldgen_main.lua
function RunScript(filename)
end

---
---UNKNOWN
---
---@param operation idk 
---@param filename idk 
---@param status idk 
---所在文件: scripts/frontend.lua
function OnSaveLoadError(operation,filename,status)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/debugtools.lua
function dprint(...)
end

---
---UNKNOWN
---
---@param inst idk 
---@param ... idk 
---所在文件: scripts/debugtools.lua
function eprint(inst,...)
end

---
---UNKNOWN
---
---@param msg idk 
---@param ... idk 
---所在文件: scripts/debugtools.lua
function printwrap(msg,...)
end

---
---UNKNOWN
---
---@param inst idk 
---@param ... idk 
---所在文件: scripts/debugtools.lua
function printsel(inst,...)
end

---
---UNKNOWN
---
---@param instance idk 
---所在文件: scripts/debugtools.lua
function instrument_userdata(instance)
end

---
---UNKNOWN
---
---@param obj idk 
---@param fn idk 
---所在文件: scripts/debugtools.lua
function tabletoliststring(obj,fn)
end

---
---UNKNOWN
---
---@param level idk 
---所在文件: scripts/debugtools.lua
function debuglocals(level)
end

---
---UNKNOWN
---
---@param ... idk 
---所在文件: scripts/debugtools.lua
function IOprint(...)
end

---
---UNKNOWN
---
---@param obj idk 
---@param fn idk 
---所在文件: scripts/debugtools.lua
function tabletodictstring(obj,fn)
end

---
---UNKNOWN
---
---@param obj idk 
---@param indent idk 
---@param recurse_levels idk 
---@param visit_table idk 
---@param is_terse idk 
---所在文件: scripts/debugtools.lua
function dumptable(obj,indent,recurse_levels,visit_table,is_terse)
end

---
---UNKNOWN
---
---@param obj idk 
---@param indent idk 
---@param recurse_levels idk 
---@param visit_table idk 
---所在文件: scripts/debugtools.lua
function dumptablequiet(obj,indent,recurse_levels,visit_table)
end

---
---UNKNOWN
---
---@param tab idk 
---@param depth idk 
---所在文件: scripts/debugtools.lua
function dtable(tab,depth)
end

---
---UNKNOWN
---
---@param thing idk 
---@param items idk 
---所在文件: scripts/debugtools.lua
function EnableDebugOnEntity(thing,items)
end

---
---UNKNOWN
---
---@param pos1 idk 
---@param pos2 idk 
---所在文件: scripts/debugtools.lua
function DrawLine(pos1,pos2)
end

---
---UNKNOWN
---
---@param thing idk 
---@param level idk 
---@param ... idk 
---所在文件: scripts/debugtools.lua
function Dbg(thing,level,...)
end

---
---UNKNOWN
---
---@param obj idk 
---@param indent idk 
---@param recurse_levels idk 
---@param root idk 
---所在文件: scripts/debugtools.lua
function ddump(obj,indent,recurse_levels,root)
end

---
---UNKNOWN
---
---@param start idk 
---@param top idk 
---@param bottom idk 
---所在文件: scripts/debugtools.lua
function debugstack(start,top,bottom)
end

---
---UNKNOWN
---
---@param linenum idk 
---所在文件: scripts/debugtools.lua
function debugstack_oneline(linenum)
end

---
---UNKNOWN
---
---@param index idk 
---所在文件: scripts/dlcsupport_worldgen.lua
function IsDLCEnabled(index)
end

---
---UNKNOWN
---
---@param tbl idk 
---所在文件: scripts/dlcsupport_worldgen.lua
function SetDLCEnabled(tbl)
end

---
---UNKNOWN
---
---所在文件: scripts/debugsounds.lua
function GetSoundDebugString()
end

---
---注册预制物
---@param name string # 预制物id
---@param fn fun():ent # 预制物主函数
---@param assets nil|table # 预制物资源表
---@param deps idk
---@param force_path_search idk
---所在文件: scripts/prefabs.lua
function Prefab(name, fn, assets, deps, force_path_search)
end


---
---配方用生成原料函数
---@param ingredienttype PrefabID # 除了预制物,还有其他类型,例如 `CHARACTER_INGREDIENT`
---@param amount number 
---@param atlas string|nil # 图集路径
---@param deconstruct idk
---@param imageoverride string|nil # tex名
---所在文件: scripts/recipe.lua
---author: lan
function Ingredient(ingredienttype, amount, atlas, deconstruct, imageoverride)
end

---为可以建造的建筑添加一个预测用的绿色虚影
---@param name any
---@param bank any
---@param build any
---@param anim any
---@param onground any
---@param snap any
---@param metersnap any
---@param scale any
---@param fixedcameraoffset any
---@param facing any
---@param postinit_fn any
---@param offset any
---@param onfailedplacement any
---
---所在文件: scripts/prefabutil.lua
---
---author:lan
function MakePlacer(name, bank, build, anim, onground, snap, metersnap, scale, fixedcameraoffset, facing, postinit_fn, offset, onfailedplacement)
end