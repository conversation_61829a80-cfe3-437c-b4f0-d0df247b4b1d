---@meta

---@class component_stretcher
local stretcher = {}

---
---@param dt idk # 
---author: 
function stretcher:OnUpdate(dt)
end

---
---author: 
function stretcher:OnEntitySleep()
end

---
---author: 
function stretcher:OnEntityWake()
end

---
---@param ratio idk # 
---author: 
function stretcher:SetWidthRatio(ratio)
end

---
---@param inst idk # 
---author: 
function stretcher:SetStretchTarget(inst)
end

---
---@param length idk # 
---author: 
function stretcher:SetRestingLength(length)
end

