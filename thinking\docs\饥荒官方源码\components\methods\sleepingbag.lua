---@meta

---@class component_sleepingbag
local sleepingbag = {}

---
---@param doer idk # 
---author: 
function sleepingbag:DoSleep(doer)
end

---
---author: 
function sleepingbag:GetSleepPhase()
end

---
---@param fn idk # 
---author: 
function sleepingbag:SetTemperatureTickFn(fn)
end

---
---@param phase idk # 
---author: 
function sleepingbag:SetSleepPhase(phase)
end

---
---author: 
function sleepingbag:InUse()
end

---
---@param nostatechange idk # 
---author: 
function sleepingbag:DoWakeUp(nostatechange)
end

