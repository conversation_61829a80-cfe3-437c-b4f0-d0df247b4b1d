---@meta

---@class component_mightydumbbell
local mightydumbbell = {}

---
---@param doer idk # 
---author: 
function mightydumbbell:IsWorkingOut(doer)
end

---
---@param doer idk # 
---author: 
function mightydumbbell:CheckEfficiency(doer)
end

---
---@param doer idk # 
---author: 
function mightydumbbell:CanWorkout(doer)
end

---
---@param doer idk # 
---author: 
function mightydumbbell:StopWorkout(doer)
end

---
---@param doer idk # 
---author: 
function mightydumbbell:CheckAttackEfficiency(doer)
end

---
---@param doer idk # 
---author: 
function mightydumbbell:DoWorkout(doer)
end

---
---@param doer idk # 
---author: 
function mightydumbbell:DoAttackWorkout(doer)
end

---
---@param wimpy idk # 
---@param normal idk # 
---@param mighty idk # 
---author: 
function mightydumbbell:SetEfficiency(wimpy,normal,mighty)
end

---
---@param consumption idk # 
---author: 
function mightydumbbell:SetConsumption(consumption)
end

---
---@param doer idk # 
---author: 
function mightydumbbell:StartWorkout(doer)
end

