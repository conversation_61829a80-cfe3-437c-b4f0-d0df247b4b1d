---@meta

---@class component_shelf
local shelf = {}

---
---@param item idk # 
---author: 
function shelf:PutItemOnShelf(item)
end

---
---@param fn idk # 
---author: 
function shelf:SetOnTakeItem(fn)
end

---
---@param fn idk # 
---author: 
function shelf:SetOnShelfItem(fn)
end

---
---@param taker idk # 
---author: 
function shelf:TakeItem(taker)
end

---
---author: 
function shelf:OnRemoveFromEntity()
end

---
---author: 
function shelf:GetDebugString()
end

