---@meta

---@class component_recallmark
local recallmark = {}

---
---@param data idk # 
---author: 
function recallmark:OnLoad(data)
end

---
---author: 
function recallmark:GetMarkedPosition()
end

---
---author: 
function recallmark:IsMarked()
end

---
---author: 
function recallmark:OnSave()
end

---
---@param rhs idk # 
---author: 
function recallmark:Copy(rhs)
end

---
---author: 
function recallmark:IsMarkedForSameShard()
end

---
---@param recall_x idk # 
---@param recall_y idk # 
---@param recall_z idk # 
---@param recall_worldid idk # 
---author: 
function recallmark:MarkPosition(recall_x,recall_y,recall_z,recall_worldid)
end

