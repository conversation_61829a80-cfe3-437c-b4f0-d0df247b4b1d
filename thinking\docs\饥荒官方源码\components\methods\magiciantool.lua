---@meta

---@class component_magiciantool
local magiciantool = {}

---
---author: 
function magiciantool:OnRemoveEntity()
end

---
---@param fn idk # 
---author: 
function magiciantool:SetOnStopUsingFn(fn)
end

---
---@param doer idk # 
---author: 
function magiciantool:OnStartUsing(doer)
end

---
---@param doer idk # 
---author: 
function magiciantool:OnStopUsing(doer)
end

---
---author: 
function magiciantool:StopUsing()
end

---
---author: 
function magiciantool:OnRemoveFromEntity()
end

---
---@param fn idk # 
---author: 
function magiciantool:SetOnStartUsingFn(fn)
end

