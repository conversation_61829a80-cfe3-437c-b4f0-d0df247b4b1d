---@meta

---@class component_walkableplatformmanager
local walkableplatformmanager = {}

---
---@param platform idk # 
---author: 
function walkableplatformmanager:RemovePlatform(platform)
end

---
---@param uid idk # 
---author: 
function walkableplatformmanager:GetPlatformWithUID(uid)
end

---
---@param dt idk # 
---author: 
function walkableplatformmanager:PostUpdate(dt)
end

---
---@param platform idk # 
---author: 
function walkableplatformmanager:UnregisterPlatform(platform)
end

---
---author: 
function walkableplatformmanager:GetNewUID()
end

---
---author: 
function walkableplatformmanager:OnSave()
end

---
---@param data idk # 
---author: 
function walkableplatformmanager:OnLoad(data)
end

---
---@param platform idk # 
---author: 
function walkableplatformmanager:AddPlatform(platform)
end

---
---@param platform idk # 
---author: 
function walkableplatformmanager:RegisterPlatform(platform)
end

