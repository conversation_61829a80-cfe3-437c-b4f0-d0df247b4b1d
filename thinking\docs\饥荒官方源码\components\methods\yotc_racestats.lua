---@meta

---@class component_yotc_racestats
local yotc_racestats = {}

---
---author: 
function yotc_racestats:GetBestStats()
end

---
---author: 
function yotc_racestats:GetDebugString()
end

---
---@param point_mod idk # 
---author: 
function yotc_racestats:ModifyStamina(point_mod)
end

---
---author: 
function yotc_racestats:GetSpeedModifier()
end

---
---@param point_mod idk # 
---author: 
function yotc_racestats:ModifyDirection(point_mod)
end

---
---@param data idk # 
---author: 
function yotc_racestats:OnLoad(data)
end

---
---@param num_points idk # 
---author: 
function yotc_racestats:AddRandomPointSpread(num_points)
end

---
---author: 
function yotc_racestats:SaveCurrentStatsAsBaseline()
end

---
---author: 
function yotc_racestats:GetNumStatPoints()
end

---
---author: 
function yotc_racestats:GetReactionModifier()
end

---
---@param num_points idk # 
---author: 
function yotc_racestats:DegradePoints(num_points)
end

---
---author: 
function yotc_racestats:GetDirectionModifier()
end

---
---author: 
function yotc_racestats:OnSave()
end

---
---author: 
function yotc_racestats:GetStaminaModifier()
end

---
---@param point_mod idk # 
---author: 
function yotc_racestats:ModifySpeed(point_mod)
end

---
---@param point_mod idk # 
---author: 
function yotc_racestats:ModifyReaction(point_mod)
end

