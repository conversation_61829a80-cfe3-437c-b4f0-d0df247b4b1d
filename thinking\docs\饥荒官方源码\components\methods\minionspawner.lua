---@meta

---@class component_minionspawner
local minionspawner = {}

---
---author: 
function minionspawner:MakeSpawnLocations()
end

---
---@param num idk # 
---@param tbl idk # 
---author: 
function minionspawner:AddPosition(num,tbl)
end

---
---@param minion idk # 
---author: 
function minionspawner:OnLostMinion(minion)
end

---
---@param tile idk # 
---author: 
function minionspawner:CheckTileCompatibility(tile)
end

---
---author: 
function minionspawner:StartNextSpawn()
end

---
---@param newents idk # 
---@param savedata idk # 
---author: 
function minionspawner:LoadPostPass(newents,savedata)
end

---
---author: 
function minionspawner:SpawnNewMinion()
end

---
---@param time idk # 
---author: 
function minionspawner:ResumeSpawn(time)
end

---
---author: 
function minionspawner:GetDebugString()
end

---
---@param time idk # 
---author: 
function minionspawner:SetSpawnInfo(time)
end

---
---author: 
function minionspawner:MaxedMinions()
end

---
---@param num idk # 
---author: 
function minionspawner:GetSpawnLocation(num)
end

---
---@param dt idk # 
---author: 
function minionspawner:LongUpdate(dt)
end

---
---author: 
function minionspawner:MakeMinion()
end

---
---author: 
function minionspawner:KillAllMinions()
end

---
---@param data idk # 
---author: 
function minionspawner:OnLoad(data)
end

---
---@param minion idk # 
---author: 
function minionspawner:TakeOwnership(minion)
end

---
---author: 
function minionspawner:GetNextSpawnTime()
end

---
---@param num idk # 
---author: 
function minionspawner:RemovePosition(num)
end

