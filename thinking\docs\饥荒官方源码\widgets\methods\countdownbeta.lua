---@meta

---@class widget_countdownbeta: widget_widget
---@overload fun(owner:idk, mode:idk, image:idk, update_name:idk, release_date:idk): widget_countdownbeta
---@field _ctor function #
---@field bg idk #
---@field daysuntiltext idk #
---@field image idk #
---@field title idk #
---@field title2 idk #
---@field reveal_image idk #
---@field smoke idk #
---@field button idk #
---@field focus_forward idk #
local countdownbeta = {}

---
---@param date idk #
---
---author: 
function countdownbeta:SetCountdownDate(date) end

