---@meta

---@class component_magician
local magician = {}

---
---author: 
function magician:DropToolOnStop()
end

---
---@param data idk # 
---author: 
function magician:OnLoad(data)
end

---
---@param item idk # 
---author: 
function magician:StartUsingTool(item)
end

---
---author: 
function magician:StopUsing()
end

---
---author: 
function magician:OnRemoveFromEntity()
end

---
---author: 
function magician:OnSave()
end

