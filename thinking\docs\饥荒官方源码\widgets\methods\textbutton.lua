---@meta

---@class widget_textbutton: widget_button
---@overload fun(name:idk): widget_textbutton
---@field _ctor function #
---@field image idk #
local textbutton = {}

---
---
---author: 
function textbutton:GetSize() end

---
---@param msg idk #
---
---author: 
function textbutton:SetText(msg) end

---
---@param r idk #
---@param g idk #
---@param b idk #
---@param a idk #
---
---author: 
function textbutton:SetColour(r,g,b,a) end

---
---@param r idk #
---@param g idk #
---@param b idk #
---@param a idk #
---
---author: 
function textbutton:SetOverColour(r,g,b,a) end

