---@meta

---@class component_planarentity
local planarentity = {}

---
---@param target idk # 
---author: 
function planarentity:OnPlanarAttackUndefended(target)
end

---
---@param damage idk # 
---@param attacker idk # 
---@param weapon idk # 
---@param spdmg idk # 
---author: 
function planarentity:AbsorbDamage(damage,attacker,weapon,spdmg)
end

---
---@param attacker idk # 
---author: 
function planarentity:OnResistNonPlanar<PERSON><PERSON>ck(attacker)
end

