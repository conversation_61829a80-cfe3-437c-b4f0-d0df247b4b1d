---@meta

---@class component_sandstormwatcher
local sandstormwatcher = {}

---
---@param mult idk # 
---author: 
function sandstormwatcher:SetSandstormSpeedMultiplier(mult)
end

---
---author: 
function sandstormwatcher:GetSandstormLevel()
end

---
---author: 
function sandstormwatcher:UpdateSandstormLevel()
end

---
---author: 
function sandstormwatcher:UpdateSandstormWalkSpeed()
end

---
---@param active idk # 
---author: 
function sandstormwatcher:ToggleSandstorms(active)
end

---
---author: 
function sandstormwatcher:OnRemoveFromEntity()
end

---
---@param level idk # 
---author: 
function sandstormwatcher:UpdateSandstormWalkSpeed_Internal(level)
end

