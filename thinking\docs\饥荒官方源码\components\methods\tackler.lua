---@meta

---@class component_tackler
local tackler = {}

---
---@param distance idk # 
---author: 
function tackler:SetDistance(distance)
end

---
---author: 
function tackler:CheckEdge()
end

---
---@param distance idk # 
---author: 
function tackler:SetEdgeDistance(distance)
end

---
---@param mult idk # 
---author: 
function tackler:SetStructureDamageMultiplier(mult)
end

---
---@param action idk # 
---@param amount idk # 
---author: 
function tackler:AddWorkAction(action,amount)
end

---
---@param fn idk # 
---author: 
function tackler:SetOnCollideFn(fn)
end

---
---@param fn idk # 
---author: 
function tackler:SetOnTrampleFn(fn)
end

---
---@param ignores idk # 
---author: 
function tackler:CheckCollision(ignores)
end

---
---author: 
function tackler:<PERSON><PERSON><PERSON><PERSON>()
end

---
---@param fn idk # 
---author: 
function tackler:SetOnStartTackleFn(fn)
end

---
---@param radius idk # 
---author: 
function tackler:Set<PERSON><PERSON><PERSON>(radius)
end

