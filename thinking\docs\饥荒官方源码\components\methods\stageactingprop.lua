---@meta

---@class component_stageactingprop
local stageactingprop = {}

---
---@param head idk # 
---@param body idk # 
---author: 
function stageactingprop:FindCostume(head,body)
end

---
---author: 
function stageactingprop:EnableProp()
end

---
---@param doer idk # 
---author: 
function stageactingprop:EndPerformance(doer)
end

---
---@param playdata idk # 
---author: 
function stageactingprop:AddPlay(playdata)
end

---
---@param dt idk # 
---author: 
function stageactingprop:OnUpdate(dt)
end

---
---@param player idk # 
---author: 
function stageactingprop:CheckCostume(player)
end

---
---@param fn idk # 
---author: 
function stageactingprop:SetDisabledFn(fn)
end

---
---@param newents idk # 
---@param data idk # 
---author: 
function stageactingprop:LoadPostPass(newents,data)
end

---
---@param doer idk # 
---author: 
function stageactingprop:FindScript(doer)
end

---
---@param time idk # 
---author: 
function stageactingprop:DisableProp(time)
end

---
---author: 
function stageactingprop:OnRemoveFromEntity()
end

---
---author: 
function stageactingprop:OnSave()
end

---
---@param doer idk # 
---author: 
function stageactingprop:ClearPerformance(doer)
end

---
---@param arch idk # 
---author: 
function stageactingprop:SpawnBirds(arch)
end

---
---@param dt idk # 
---author: 
function stageactingprop:LongUpdate(dt)
end

---
---@param next_act idk # 
---author: 
function stageactingprop:FinishAct(next_act)
end

---
---@param script_name idk # 
---@param script_content idk # 
---author: 
function stageactingprop:AddGeneralScript(script_name,script_content)
end

---
---@param fn idk # 
---author: 
function stageactingprop:SetEnabledFn(fn)
end

---
---@param doer idk # 
---author: 
function stageactingprop:CollectCast(doer)
end

---
---@param doer idk # 
---author: 
function stageactingprop:DoPerformance(doer)
end

