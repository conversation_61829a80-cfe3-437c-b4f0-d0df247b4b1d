---@meta

---@class component_miasmawatcher
local miasmawatcher = {}

---
---author: 
function miasmawatcher:UpdateMiasmaWalkSpeed()
end

---
---@param active idk # 
---author: 
function miasmawatcher:Toggle<PERSON>iasma(active)
end

---
---@param mult idk # 
---author: 
function miasmawatcher:SetMiasmaSpeedMultiplier(mult)
end

---
---@param src idk # 
---author: 
function miasmawatcher:AddMiasmaSource(src)
end

---
---@param src idk # 
---author: 
function miasmawatcher:RemoveMiasmaSource(src)
end

---
---author: 
function miasmawatcher:OnRemoveFromEntity()
end

---
---author: 
function miasmawatcher:IsInMiasma()
end

