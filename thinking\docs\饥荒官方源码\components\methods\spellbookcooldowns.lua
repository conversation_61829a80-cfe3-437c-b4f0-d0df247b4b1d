---@meta

---@class component_spellbookcooldowns
local spellbookcooldowns = {}

---
---@param spellname idk # 
---author: 
function spellbookcooldowns:GetSpellCooldownPercent(spellname)
end

---
---author: 
function spellbookcooldowns:GetDebugString()
end

---
---@param cd idk # 
---author: 
function spellbookcooldowns:RegisterSpellbookCooldown(cd)
end

---
---@param spellname idk # 
---author: 
function spellbookcooldowns:StopSpellCooldown(spellname)
end

---
---@param spellname idk # 
---@param duration idk # 
---author: 
function spellbookcooldowns:RestartSpellCooldown(spellname,duration)
end

