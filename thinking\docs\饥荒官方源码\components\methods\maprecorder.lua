---@meta

---@class component_maprecorder
local maprecorder = {}

---
---@param fn idk # 
---author: 
function maprecorder:SetOnDataChangedFn(fn)
end

---
---author: 
function maprecorder:IsCurrentWorld()
end

---
---author: 
function maprecorder:OnSave()
end

---
---author: 
function maprecorder:HasData()
end

---
---@param target idk # 
---author: 
function maprecorder:RecordMap(target)
end

---
---@param data idk # 
---author: 
function maprecorder:OnLoad(data)
end

---
---@param target idk # 
---author: 
function maprecorder:TeachMap(target)
end

---
---author: 
function maprecorder:ClearMap()
end

