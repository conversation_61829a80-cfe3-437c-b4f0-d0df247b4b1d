---@meta

---@class widget_kitcoonpoop: widget_widget
---@overload fun(kit:idk, gamescreen:idk, profile:idk): widget_kitcoonpoop
---@field _ctor function #
---@field kit idk #
---@field gamescreen idk #
---@field anim idk #
---@field animstate idk #
---@field onclick idk #
local kitcoonpoop = {}

---
---
---author: 
function kitcoonpoop:OnGainFocus() end

---
---@param dt idk #
---
---author: 
function kitcoonpoop:OnUpdate(dt) end

---
---@param control idk #
---@param down idk #
---
---author: 
function kitcoonpoop:OnControl(control, down) end

