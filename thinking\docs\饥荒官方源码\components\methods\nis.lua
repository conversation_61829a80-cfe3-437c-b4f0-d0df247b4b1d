---@meta

---@class component_nis
local nis = {}

---
---author: 
function nis:OnRemoveEntity()
end

---
---author: 
function nis:OnFinish()
end

---
---@param name idk # 
---author: 
function nis:Set<PERSON>ame(name)
end

---
---author: 
function nis:Cancel()
end

---
---@param lines idk # 
---author: 
function nis:Play(lines)
end

---
---author: 
function nis:OnClick()
end

---
---@param fn idk # 
---author: 
function nis:SetScript(fn)
end

---
---@param fn idk # 
---author: 
function nis:SetCancel(fn)
end

---
---@param fn idk # 
---author: 
function nis:SetInit(fn)
end

