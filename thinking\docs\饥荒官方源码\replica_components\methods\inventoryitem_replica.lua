---@meta

---@class replica_inventoryitem
local replica_inventoryitem = {}

---
---@param canbepickedup idk # 
---author: 
function replica_inventoryitem:SetCanBePickedUp(canbepickedup)
end

---
---author: 
function replica_inventoryitem:OnRemoveFromEntity()
end

---
---author: 
function replica_inventoryitem:DetachClassified()
end

---
---@param classified idk # 
---author: 
function replica_inventoryitem:AttachClassified(classified)
end

---
---@param cangoincontainer idk # 
---author: 
function replica_inventoryitem:SetCanGoInContainer(cangoincontainer)
end

---
---author: 
function replica_inventoryitem:IsHeld()
end

---
---@param pt idk # 
---@param mouseover idk # 
---@param deployer idk # 
---@param rot idk # 
---author: 
function replica_inventoryitem:CanDeploy(pt,mouseover,deployer,rot)
end

---
---@param atlasname idk # 
---author: 
function replica_inventoryitem:SetAtlas(atlasname)
end

---
---@param t idk # 
---author: 
function replica_inventoryitem:SetChargeTime(t)
end

---
---@param iswet idk # 
---author: 
function replica_inventoryitem:SetIsWet(iswet)
end

---
---author: 
function replica_inventoryitem:GetPickupPos()
end

---
---@param deployspacing idk # 
---author: 
function replica_inventoryitem:SetDeploySpacing(deployspacing)
end

---
---@param imagename idk # 
---author: 
function replica_inventoryitem:SetImage(imagename)
end

---
---author: 
function replica_inventoryitem:GetAtlas()
end

---
---@param walkspeedmult idk # 
---author: 
function replica_inventoryitem:SetWalkSpeedMult(walkspeedmult)
end

---
---author: 
function replica_inventoryitem:DeploySpacingRadius()
end

---
---@param canonlygoinpocket idk # 
---author: 
function replica_inventoryitem:SetCanOnlyGoInPocket(canonlygoinpocket)
end

---
---@param restrictedtag idk # 
---author: 
function replica_inventoryitem:SetDeployRestrictedTag(restrictedtag)
end

---
---author: 
function replica_inventoryitem:OnRemoveEntity()
end

---
---@param owner idk # 
---author: 
function replica_inventoryitem:SetOwner(owner)
end

---
---@param deployer idk # 
---author: 
function replica_inventoryitem:IsDeployable(deployer)
end

---
---@param guy idk # 
---author: 
function replica_inventoryitem:IsGrandOwner(guy)
end

---
---author: 
function replica_inventoryitem:GetEquipRestrictedTag()
end

---
---author: 
function replica_inventoryitem:GetImage()
end

---
---author: 
function replica_inventoryitem:IsAcidSizzling()
end

---
---author: 
function replica_inventoryitem:IsWet()
end

---
---author: 
function replica_inventoryitem:GetWalkSpeedMult()
end

---
---author: 
function replica_inventoryitem:GetMoisture()
end

---
---@param moisture idk # 
---author: 
function replica_inventoryitem:SetMoistureLevel(moisture)
end

---
---@param restrictedtag idk # 
---author: 
function replica_inventoryitem:SetEquipRestrictedTag(restrictedtag)
end

---
---author: 
function replica_inventoryitem:IsWeapon()
end

---
---author: 
function replica_inventoryitem:CanGoInContainer()
end

---
---@param deploymode idk # 
---author: 
function replica_inventoryitem:SetDeployMode(deploymode)
end

---
---@param pos idk # 
---author: 
function replica_inventoryitem:SetPickupPos(pos)
end

---
---author: 
function replica_inventoryitem:CanBePickedUp()
end

---
---@param guy idk # 
---author: 
function replica_inventoryitem:IsHeldBy(guy)
end

---
---@param attackrange idk # 
---author: 
function replica_inventoryitem:SetAttackRange(attackrange)
end

---
---@param isacidsizzling idk # 
---author: 
function replica_inventoryitem:SetIsAcidSizzling(isacidsizzling)
end

---
---@param usegridplacer idk # 
---author: 
function replica_inventoryitem:SetUseGridPlacer(usegridplacer)
end

---
---author: 
function replica_inventoryitem:AttackRange()
end

---
---@param imagename idk # 
---author: 
function replica_inventoryitem:OverrideImage(imagename)
end

---
---author: 
function replica_inventoryitem:CanOnlyGoInPocket()
end

---
---author: 
function replica_inventoryitem:DeserializeUsage()
end

---
---author: 
function replica_inventoryitem:GetDeployPlacerName()
end

---
---author: 
function replica_inventoryitem:SerializeUsage()
end

