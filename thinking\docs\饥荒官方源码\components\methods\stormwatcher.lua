---@meta

---@class component_stormwatcher
local stormwatcher = {}

---
---@param data idk # 
---author: 
function stormwatcher:CheckStorms(data)
end

---
---@param data idk # 
---author: 
function stormwatcher:UpdateStorms(data)
end

---
---@param stormtype idk # 
---author: 
function stormwatcher:GetStormLevel(stormtype)
end

---
---@param dt idk # 
---author: 
function stormwatcher:OnUpdate(dt)
end

---
---author: 
function stormwatcher:UpdateStormLevel()
end

---
---@param inst idk # 
---author: 
function stormwatcher:GetCurrentStorm(inst)
end

